import { NextResponse } from 'next/server'

export async function POST() {
  const orchestratorBase = process.env.CALL_ORCHESTRATOR_URL

  if (!orchestratorBase) {
    return NextResponse.json({ error: 'Call Orchestrator URL not configured' }, { status: 500 })
  }

  const res = await fetch(`${orchestratorBase.replace(/\/$/, '')}/api/v1/calls`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
  })

  if (res.ok) {
    const json = await res.json()
    return NextResponse.json(json, { status: 201 })
  }
}
