import asyncio
import json
import os
import uuid
from datetime import datetime, timezone
from pathlib import Path

from fastapi import FastAPI, HTTPException, Request
from fastapi.responses import HTMLResponse, FileResponse
from pydantic import BaseModel
from aiokafka import AIOKafkaProducer


app = FastAPI(title="Mock Caller (SIP Gateway)")


# --- Config ---
KAFKA_BOOTSTRAP_SERVERS = os.getenv("KAFKA_BOOTSTRAP_SERVERS", "kafka:29092")
VOICE_ROUTER_WS = os.getenv("VOICE_ROUTER_WS", "ws://voice-router:3001")
SERVICE_NAME = os.getenv("SERVICE_NAME", "mock-caller")

# Track call -> mediaSessionId mapping for UI
CALL_SESSION_MAP: dict[str, str] = {}

producer: AIOKafkaProducer | None = None


class CallConnectRequest(BaseModel):
    call_id: str
    media_session_id: str


async def get_producer() -> AIOKafkaProducer:
    global producer
    if producer is None:
        producer = AIOKafkaProducer(bootstrap_servers=KAFKA_BOOTSTRAP_SERVERS)
        await producer.start()
    return producer


@app.on_event("shutdown")
async def shutdown_event():
    global producer
    if producer:
        await producer.stop()
        producer = None


@app.post("/internal/call")
async def create_call():
    """Simulate an incoming SIP call by publishing call.started to Kafka.

    Topic expected by queuing-service: call-orchestrator.call-started
    Payload fields align with queuing-service's CallStartedEvent model.
    """
    try:
        call_id = str(uuid.uuid4())
        event = {
            "event_id": str(uuid.uuid4()),
            "event_type": "call.started",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "source_service": SERVICE_NAME,
            "call_id": call_id,
            # Optional routing hints; keep minimal for now
            "priority": "NORMAL",
            "required_role": "OPERATOR",
            "required_skills": [],
            "language_pair": None,
            "metadata": {},
        }
        prod = await get_producer()
        await prod.send_and_wait("call-orchestrator.call-started", json.dumps(event).encode("utf-8"))
        return {"status": "published", "call_id": call_id}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to publish event: {e}")


@app.post("/internal/connect")
async def connect_to_call(request: CallConnectRequest):
    """Record media_session_id for a call so a human can connect via the UI.

    The Call Orchestrator will POST here when it has the media_session_id.
    """
    CALL_SESSION_MAP[request.call_id] = request.media_session_id
    return {"status": "ok"}


# ========= MOCK UI =========
# This UI is used to simulate a caller
@app.get("/ui/test", response_class=HTMLResponse)
async def test_ui():
    """Render the test UI."""
    with open(Path(__file__).parent / "mock" / "test_client.html", "r") as f:
        return HTMLResponse(content=f.read())


@app.get("/mock/mediasoup-client.min.js")
async def mediasoup_client():
    """Serve mediasoup-client library."""
    return FileResponse(Path(__file__).parent / "mock" / "mediasoup-client.min.js")
