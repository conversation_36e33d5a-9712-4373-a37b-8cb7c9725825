from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    host: str = "0.0.0.0"
    port: int = 8000
    debug: bool = False
    voice_router_ws: str = "ws://voice-router:3001"
    kafka_bootstrap_servers: str = "kafka:29092"
    service_name: str = "mock-caller"

    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",
    )