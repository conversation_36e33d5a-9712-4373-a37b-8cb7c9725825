(()=>{var e={11:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Chrome74=void 0;const s=r(7363),i=r(2994),a=r(3953),n=r(8046),o=r(4893),c=r(3303),d=r(5544),p=r(5938),l=r(4256),h=r(1305),u=new i.<PERSON>gger("Chrome74"),m="Chrome74",f={OS:1024,MIS:1024};class g extends a.EnhancedEventEmitter{_closed=!1;_direction;_remoteSdp;_getSendExtendedRtpCapabilities;_forcedLocalDtlsRole;_pc;_mapMidTransceiver=new Map;_sendStream=new MediaStream;_hasDataChannelMediaSection=!1;_nextSendSctpStreamId=0;_transportReady=!1;static createFactory(){return{name:m,factory:e=>new g(e),getNativeRtpCapabilities:async()=>{u.debug("getNativeRtpCapabilities()");let e=new RTCPeerConnection({iceServers:[],iceTransportPolicy:"all",bundlePolicy:"max-bundle",rtcpMuxPolicy:"require"});try{e.addTransceiver("audio"),e.addTransceiver("video");const t=await e.createOffer();try{e.close()}catch(e){}e=void 0;const r=s.parse(t.sdp);return g.getLocalRtpCapabilities(r)}catch(t){try{e?.close()}catch(e){}throw e=void 0,t}},getNativeSctpCapabilities:async()=>(u.debug("getNativeSctpCapabilities()"),{numStreams:f})}}static getLocalRtpCapabilities(e){const t=d.extractRtpCapabilities({sdpObject:e});return l.addNackSupportForOpus(t),t}constructor({direction:e,iceParameters:t,iceCandidates:r,dtlsParameters:s,sctpParameters:i,iceServers:a,iceTransportPolicy:n,additionalSettings:o,getSendExtendedRtpCapabilities:c}){super(),u.debug("constructor()"),this._direction=e,this._remoteSdp=new h.RemoteSdp({iceParameters:t,iceCandidates:r,dtlsParameters:s,sctpParameters:i}),this._getSendExtendedRtpCapabilities=c,s.role&&"auto"!==s.role&&(this._forcedLocalDtlsRole="server"===s.role?"client":"server"),this._pc=new RTCPeerConnection({iceServers:a??[],iceTransportPolicy:n??"all",bundlePolicy:"max-bundle",rtcpMuxPolicy:"require",...o}),this._pc.addEventListener("icegatheringstatechange",this.onIceGatheringStateChange),this._pc.addEventListener("icecandidateerror",this.onIceCandidateError),this._pc.connectionState?this._pc.addEventListener("connectionstatechange",this.onConnectionStateChange):(u.warn("run() | pc.connectionState not supported, using pc.iceConnectionState"),this._pc.addEventListener("iceconnectionstatechange",this.onIceConnectionStateChange))}get name(){return m}close(){if(u.debug("close()"),!this._closed){this._closed=!0;try{this._pc.close()}catch(e){}this._pc.removeEventListener("icegatheringstatechange",this.onIceGatheringStateChange),this._pc.removeEventListener("icecandidateerror",this.onIceCandidateError),this._pc.removeEventListener("connectionstatechange",this.onConnectionStateChange),this._pc.removeEventListener("iceconnectionstatechange",this.onIceConnectionStateChange),this.emit("@close"),super.close()}}async updateIceServers(e){this.assertNotClosed(),u.debug("updateIceServers()");const t=this._pc.getConfiguration();t.iceServers=e,this._pc.setConfiguration(t)}async restartIce(e){if(this.assertNotClosed(),u.debug("restartIce()"),this._remoteSdp.updateIceParameters(e),this._transportReady)if("send"===this._direction){const e=await this._pc.createOffer({iceRestart:!0});u.debug("restartIce() | calling pc.setLocalDescription() [offer:%o]",e),await this._pc.setLocalDescription(e);const t={type:"answer",sdp:this._remoteSdp.getSdp()};u.debug("restartIce() | calling pc.setRemoteDescription() [answer:%o]",t),await this._pc.setRemoteDescription(t)}else{const e={type:"offer",sdp:this._remoteSdp.getSdp()};u.debug("restartIce() | calling pc.setRemoteDescription() [offer:%o]",e),await this._pc.setRemoteDescription(e);const t=await this._pc.createAnswer();u.debug("restartIce() | calling pc.setLocalDescription() [answer:%o]",t),await this._pc.setLocalDescription(t)}}async getTransportStats(){return this.assertNotClosed(),this._pc.getStats()}async send({track:e,encodings:t,codecOptions:r,codec:i}){this.assertNotClosed(),this.assertSendDirection(),u.debug("send() [kind:%s, track.id:%s]",e.kind,e.id),t&&t.length>1&&t.forEach((e,t)=>{e.rid=`r${t}`});const a=this._remoteSdp.getNextMediaSectionIdx(),o=this._pc.addTransceiver(e,{direction:"sendonly",streams:[this._sendStream],sendEncodings:t});let l=await this._pc.createOffer(),h=s.parse(l.sdp);h.extmapAllowMixed&&this._remoteSdp.setSessionExtmapAllowMixed();const m=g.getLocalRtpCapabilities(h),f=this._getSendExtendedRtpCapabilities(m),_=n.getSendingRtpParameters(e.kind,f);_.codecs=n.reduceCodecs(_.codecs,i);const b=n.getSendingRemoteRtpParameters(e.kind,f);b.codecs=n.reduceCodecs(b.codecs,i),this._transportReady||await this.setupTransport({localDtlsRole:this._forcedLocalDtlsRole??"client",localSdpObject:h});let w=!1;const v=(0,c.parse)((t??[{}])[0].scalabilityMode);let y;t&&1===t.length&&v.spatialLayers>1&&"video/vp9"===_.codecs[0].mimeType.toLowerCase()&&(u.debug("send() | enabling legacy simulcast for VP9 SVC"),w=!0,h=s.parse(l.sdp),y=h.media[a.idx],p.addLegacySimulcast({offerMediaObject:y,numStreams:v.spatialLayers}),l={type:"offer",sdp:s.write(h)}),u.debug("send() | calling pc.setLocalDescription() [offer:%o]",l),await this._pc.setLocalDescription(l);const S=o.mid;if(_.mid=S,h=s.parse(this._pc.localDescription.sdp),y=h.media[a.idx],_.rtcp.cname=d.getCname({offerMediaObject:y}),t)if(1===t.length){let e=p.getRtpEncodings({offerMediaObject:y});Object.assign(e[0],t[0]),w&&(e=[e[0]]),_.encodings=e}else _.encodings=t;else _.encodings=p.getRtpEncodings({offerMediaObject:y});if(_.encodings.length>1&&("video/vp8"===_.codecs[0].mimeType.toLowerCase()||"video/h264"===_.codecs[0].mimeType.toLowerCase()))for(const e of _.encodings)e.scalabilityMode?e.scalabilityMode=`L1T${v.temporalLayers}`:e.scalabilityMode="L1T3";this._remoteSdp.send({offerMediaObject:y,reuseMid:a.reuseMid,offerRtpParameters:_,answerRtpParameters:b,codecOptions:r});const C={type:"answer",sdp:this._remoteSdp.getSdp()};return u.debug("send() | calling pc.setRemoteDescription() [answer:%o]",C),await this._pc.setRemoteDescription(C),this._mapMidTransceiver.set(S,o),{localId:S,rtpParameters:_,rtpSender:o.sender}}async stopSending(e){if(this.assertSendDirection(),u.debug("stopSending() [localId:%s]",e),this._closed)return;const t=this._mapMidTransceiver.get(e);if(!t)throw new Error("associated RTCRtpTransceiver not found");if(t.sender.replaceTrack(null),this._pc.removeTrack(t.sender),this._remoteSdp.closeMediaSection(t.mid))try{t.stop()}catch(e){}const r=await this._pc.createOffer();u.debug("stopSending() | calling pc.setLocalDescription() [offer:%o]",r),await this._pc.setLocalDescription(r);const s={type:"answer",sdp:this._remoteSdp.getSdp()};u.debug("stopSending() | calling pc.setRemoteDescription() [answer:%o]",s),await this._pc.setRemoteDescription(s),this._mapMidTransceiver.delete(e)}async pauseSending(e){this.assertNotClosed(),this.assertSendDirection(),u.debug("pauseSending() [localId:%s]",e);const t=this._mapMidTransceiver.get(e);if(!t)throw new Error("associated RTCRtpTransceiver not found");t.direction="inactive",this._remoteSdp.pauseMediaSection(e);const r=await this._pc.createOffer();u.debug("pauseSending() | calling pc.setLocalDescription() [offer:%o]",r),await this._pc.setLocalDescription(r);const s={type:"answer",sdp:this._remoteSdp.getSdp()};u.debug("pauseSending() | calling pc.setRemoteDescription() [answer:%o]",s),await this._pc.setRemoteDescription(s)}async resumeSending(e){this.assertNotClosed(),this.assertSendDirection(),u.debug("resumeSending() [localId:%s]",e);const t=this._mapMidTransceiver.get(e);if(this._remoteSdp.resumeSendingMediaSection(e),!t)throw new Error("associated RTCRtpTransceiver not found");t.direction="sendonly";const r=await this._pc.createOffer();u.debug("resumeSending() | calling pc.setLocalDescription() [offer:%o]",r),await this._pc.setLocalDescription(r);const s={type:"answer",sdp:this._remoteSdp.getSdp()};u.debug("resumeSending() | calling pc.setRemoteDescription() [answer:%o]",s),await this._pc.setRemoteDescription(s)}async replaceTrack(e,t){this.assertNotClosed(),this.assertSendDirection(),t?u.debug("replaceTrack() [localId:%s, track.id:%s]",e,t.id):u.debug("replaceTrack() [localId:%s, no track]",e);const r=this._mapMidTransceiver.get(e);if(!r)throw new Error("associated RTCRtpTransceiver not found");await r.sender.replaceTrack(t)}async setMaxSpatialLayer(e,t){this.assertNotClosed(),this.assertSendDirection(),u.debug("setMaxSpatialLayer() [localId:%s, spatialLayer:%s]",e,t);const r=this._mapMidTransceiver.get(e);if(!r)throw new Error("associated RTCRtpTransceiver not found");const s=r.sender.getParameters();s.encodings.forEach((e,r)=>{e.active=r<=t}),await r.sender.setParameters(s),this._remoteSdp.muxMediaSectionSimulcast(e,s.encodings);const i=await this._pc.createOffer();u.debug("setMaxSpatialLayer() | calling pc.setLocalDescription() [offer:%o]",i),await this._pc.setLocalDescription(i);const a={type:"answer",sdp:this._remoteSdp.getSdp()};u.debug("setMaxSpatialLayer() | calling pc.setRemoteDescription() [answer:%o]",a),await this._pc.setRemoteDescription(a)}async setRtpEncodingParameters(e,t){this.assertNotClosed(),this.assertSendDirection(),u.debug("setRtpEncodingParameters() [localId:%s, params:%o]",e,t);const r=this._mapMidTransceiver.get(e);if(!r)throw new Error("associated RTCRtpTransceiver not found");const s=r.sender.getParameters();s.encodings.forEach((e,r)=>{s.encodings[r]={...e,...t}}),await r.sender.setParameters(s),this._remoteSdp.muxMediaSectionSimulcast(e,s.encodings);const i=await this._pc.createOffer();u.debug("setRtpEncodingParameters() | calling pc.setLocalDescription() [offer:%o]",i),await this._pc.setLocalDescription(i);const a={type:"answer",sdp:this._remoteSdp.getSdp()};u.debug("setRtpEncodingParameters() | calling pc.setRemoteDescription() [answer:%o]",a),await this._pc.setRemoteDescription(a)}async getSenderStats(e){this.assertNotClosed(),this.assertSendDirection();const t=this._mapMidTransceiver.get(e);if(!t)throw new Error("associated RTCRtpTransceiver not found");return t.sender.getStats()}async sendDataChannel({ordered:e,maxPacketLifeTime:t,maxRetransmits:r,label:i,protocol:a}){this.assertNotClosed(),this.assertSendDirection();const n={negotiated:!0,id:this._nextSendSctpStreamId,ordered:e,maxPacketLifeTime:t,maxRetransmits:r,protocol:a};u.debug("sendDataChannel() [options:%o]",n);const o=this._pc.createDataChannel(i,n);if(this._nextSendSctpStreamId=++this._nextSendSctpStreamId%f.MIS,!this._hasDataChannelMediaSection){const e=await this._pc.createOffer(),t=s.parse(e.sdp),r=t.media.find(e=>"application"===e.type);this._transportReady||await this.setupTransport({localDtlsRole:this._forcedLocalDtlsRole??"client",localSdpObject:t}),u.debug("sendDataChannel() | calling pc.setLocalDescription() [offer:%o]",e),await this._pc.setLocalDescription(e),this._remoteSdp.sendSctpAssociation({offerMediaObject:r});const i={type:"answer",sdp:this._remoteSdp.getSdp()};u.debug("sendDataChannel() | calling pc.setRemoteDescription() [answer:%o]",i),await this._pc.setRemoteDescription(i),this._hasDataChannelMediaSection=!0}return{dataChannel:o,sctpStreamParameters:{streamId:n.id,ordered:n.ordered,maxPacketLifeTime:n.maxPacketLifeTime,maxRetransmits:n.maxRetransmits}}}async receive(e){this.assertNotClosed(),this.assertRecvDirection();const t=[],r=new Map;for(const t of e){const{trackId:e,kind:s,rtpParameters:i,streamId:a}=t;u.debug("receive() [trackId:%s, kind:%s]",e,s);const n=i.mid??String(this._mapMidTransceiver.size);r.set(e,n),this._remoteSdp.receive({mid:n,kind:s,offerRtpParameters:i,streamId:a??i.rtcp.cname,trackId:e})}const i={type:"offer",sdp:this._remoteSdp.getSdp()};u.debug("receive() | calling pc.setRemoteDescription() [offer:%o]",i),await this._pc.setRemoteDescription(i);let a=await this._pc.createAnswer();const n=s.parse(a.sdp);for(const t of e){const{trackId:e,rtpParameters:s}=t,i=r.get(e),a=n.media.find(e=>String(e.mid)===i);d.applyCodecParameters({offerRtpParameters:s,answerMediaObject:a})}a={type:"answer",sdp:s.write(n)},this._transportReady||await this.setupTransport({localDtlsRole:this._forcedLocalDtlsRole??"client",localSdpObject:n}),u.debug("receive() | calling pc.setLocalDescription() [answer:%o]",a),await this._pc.setLocalDescription(a);for(const s of e){const{trackId:e}=s,i=r.get(e),a=this._pc.getTransceivers().find(e=>e.mid===i);if(!a)throw new Error("new RTCRtpTransceiver not found");this._mapMidTransceiver.set(i,a),t.push({localId:i,track:a.receiver.track,rtpReceiver:a.receiver})}return t}async stopReceiving(e){if(this.assertRecvDirection(),this._closed)return;for(const t of e){u.debug("stopReceiving() [localId:%s]",t);const e=this._mapMidTransceiver.get(t);if(!e)throw new Error("associated RTCRtpTransceiver not found");this._remoteSdp.closeMediaSection(e.mid)}const t={type:"offer",sdp:this._remoteSdp.getSdp()};u.debug("stopReceiving() | calling pc.setRemoteDescription() [offer:%o]",t),await this._pc.setRemoteDescription(t);const r=await this._pc.createAnswer();u.debug("stopReceiving() | calling pc.setLocalDescription() [answer:%o]",r),await this._pc.setLocalDescription(r);for(const t of e)this._mapMidTransceiver.delete(t)}async pauseReceiving(e){this.assertNotClosed(),this.assertRecvDirection();for(const t of e){u.debug("pauseReceiving() [localId:%s]",t);const e=this._mapMidTransceiver.get(t);if(!e)throw new Error("associated RTCRtpTransceiver not found");e.direction="inactive",this._remoteSdp.pauseMediaSection(t)}const t={type:"offer",sdp:this._remoteSdp.getSdp()};u.debug("pauseReceiving() | calling pc.setRemoteDescription() [offer:%o]",t),await this._pc.setRemoteDescription(t);const r=await this._pc.createAnswer();u.debug("pauseReceiving() | calling pc.setLocalDescription() [answer:%o]",r),await this._pc.setLocalDescription(r)}async resumeReceiving(e){this.assertNotClosed(),this.assertRecvDirection();for(const t of e){u.debug("resumeReceiving() [localId:%s]",t);const e=this._mapMidTransceiver.get(t);if(!e)throw new Error("associated RTCRtpTransceiver not found");e.direction="recvonly",this._remoteSdp.resumeReceivingMediaSection(t)}const t={type:"offer",sdp:this._remoteSdp.getSdp()};u.debug("resumeReceiving() | calling pc.setRemoteDescription() [offer:%o]",t),await this._pc.setRemoteDescription(t);const r=await this._pc.createAnswer();u.debug("resumeReceiving() | calling pc.setLocalDescription() [answer:%o]",r),await this._pc.setLocalDescription(r)}async getReceiverStats(e){this.assertNotClosed(),this.assertRecvDirection();const t=this._mapMidTransceiver.get(e);if(!t)throw new Error("associated RTCRtpTransceiver not found");return t.receiver.getStats()}async receiveDataChannel({sctpStreamParameters:e,label:t,protocol:r}){this.assertNotClosed(),this.assertRecvDirection();const{streamId:i,ordered:a,maxPacketLifeTime:n,maxRetransmits:o}=e,c={negotiated:!0,id:i,ordered:a,maxPacketLifeTime:n,maxRetransmits:o,protocol:r};u.debug("receiveDataChannel() [options:%o]",c);const d=this._pc.createDataChannel(t,c);if(!this._hasDataChannelMediaSection){this._remoteSdp.receiveSctpAssociation();const e={type:"offer",sdp:this._remoteSdp.getSdp()};u.debug("receiveDataChannel() | calling pc.setRemoteDescription() [offer:%o]",e),await this._pc.setRemoteDescription(e);const t=await this._pc.createAnswer();if(!this._transportReady){const e=s.parse(t.sdp);await this.setupTransport({localDtlsRole:this._forcedLocalDtlsRole??"client",localSdpObject:e})}u.debug("receiveDataChannel() | calling pc.setRemoteDescription() [answer:%o]",t),await this._pc.setLocalDescription(t),this._hasDataChannelMediaSection=!0}return{dataChannel:d}}async setupTransport({localDtlsRole:e,localSdpObject:t}){t||(t=s.parse(this._pc.localDescription.sdp));const r=d.extractDtlsParameters({sdpObject:t});r.role=e,this._remoteSdp.updateDtlsRole("client"===e?"server":"client"),await new Promise((e,t)=>{this.safeEmit("@connect",{dtlsParameters:r},e,t)}),this._transportReady=!0}onIceGatheringStateChange=()=>{this.emit("@icegatheringstatechange",this._pc.iceGatheringState)};onIceCandidateError=e=>{this.emit("@icecandidateerror",e)};onConnectionStateChange=()=>{this.emit("@connectionstatechange",this._pc.connectionState)};onIceConnectionStateChange=()=>{switch(this._pc.iceConnectionState){case"checking":this.emit("@connectionstatechange","connecting");break;case"connected":case"completed":this.emit("@connectionstatechange","connected");break;case"failed":this.emit("@connectionstatechange","failed");break;case"disconnected":this.emit("@connectionstatechange","disconnected");break;case"closed":this.emit("@connectionstatechange","closed")}};assertNotClosed(){if(this._closed)throw new o.InvalidStateError("method called in a closed handler")}assertSendDirection(){if("send"!==this._direction)throw new Error('method can just be called for handlers with "send" direction')}assertRecvDirection(){if("recv"!==this._direction)throw new Error('method can just be called for handlers with "recv" direction')}}t.Chrome74=g},76:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.debug=t.testFakeParameters=t.FakeHandler=t.enhancedEvents=t.ortc=t.parseScalabilityMode=t.detectDeviceAsync=t.detectDevice=t.Device=t.version=t.types=void 0;const s=r(7833);t.debug=s.default,t.types=r(8057),t.version="3.15.2";var i=r(6004);Object.defineProperty(t,"Device",{enumerable:!0,get:function(){return i.Device}}),Object.defineProperty(t,"detectDevice",{enumerable:!0,get:function(){return i.detectDevice}}),Object.defineProperty(t,"detectDeviceAsync",{enumerable:!0,get:function(){return i.detectDeviceAsync}});var a=r(3303);Object.defineProperty(t,"parseScalabilityMode",{enumerable:!0,get:function(){return a.parse}}),t.ortc=r(8046),t.enhancedEvents=r(3953);var n=r(2731);Object.defineProperty(t,"FakeHandler",{enumerable:!0,get:function(){return n.FakeHandler}}),t.testFakeParameters=r(5248)},182:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.version=t.validate=t.v7=t.v6ToV1=t.v6=t.v5=t.v4=t.v3=t.v1ToV6=t.v1=t.stringify=t.parse=t.NIL=t.MAX=void 0;var s=r(2196);Object.defineProperty(t,"MAX",{enumerable:!0,get:function(){return s.default}});var i=r(3465);Object.defineProperty(t,"NIL",{enumerable:!0,get:function(){return i.default}});var a=r(1797);Object.defineProperty(t,"parse",{enumerable:!0,get:function(){return a.default}});var n=r(6011);Object.defineProperty(t,"stringify",{enumerable:!0,get:function(){return n.default}});var o=r(1425);Object.defineProperty(t,"v1",{enumerable:!0,get:function(){return o.default}});var c=r(6568);Object.defineProperty(t,"v1ToV6",{enumerable:!0,get:function(){return c.default}});var d=r(591);Object.defineProperty(t,"v3",{enumerable:!0,get:function(){return d.default}});var p=r(8286);Object.defineProperty(t,"v4",{enumerable:!0,get:function(){return p.default}});var l=r(4557);Object.defineProperty(t,"v5",{enumerable:!0,get:function(){return l.default}});var h=r(6356);Object.defineProperty(t,"v6",{enumerable:!0,get:function(){return h.default}});var u=r(268);Object.defineProperty(t,"v6ToV1",{enumerable:!0,get:function(){return u.default}});var m=r(4299);Object.defineProperty(t,"v7",{enumerable:!0,get:function(){return m.default}});var f=r(9746);Object.defineProperty(t,"validate",{enumerable:!0,get:function(){return f.default}});var g=r(2770);Object.defineProperty(t,"version",{enumerable:!0,get:function(){return g.default}})},268:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const s=r(1797),i=r(6011);t.default=function(e){const t=(r="string"==typeof e?(0,s.default)(e):e,Uint8Array.of((15&r[3])<<4|r[4]>>4&15,(15&r[4])<<4|(240&r[5])>>4,(15&r[5])<<4|15&r[6],r[7],(15&r[1])<<4|(240&r[2])>>4,(15&r[2])<<4|(240&r[3])>>4,16|(240&r[0])>>4,(15&r[0])<<4|(240&r[1])>>4,r[8],r[9],r[10],r[11],r[12],r[13],r[14],r[15]));var r;return"string"==typeof e?(0,i.unsafeStringify)(t):t}},338:(e,t)=>{"use strict";function r(e){return 14+(e+64>>>9<<4)+1}function s(e,t){const r=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(r>>16)<<16|65535&r}function i(e,t,r,i,a,n){return s((o=s(s(t,e),s(i,n)))<<(c=a)|o>>>32-c,r);var o,c}function a(e,t,r,s,a,n,o){return i(t&r|~t&s,e,t,a,n,o)}function n(e,t,r,s,a,n,o){return i(t&s|r&~s,e,t,a,n,o)}function o(e,t,r,s,a,n,o){return i(t^r^s,e,t,a,n,o)}function c(e,t,r,s,a,n,o){return i(r^(t|~s),e,t,a,n,o)}Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return function(e){const t=new Uint8Array(4*e.length);for(let r=0;r<4*e.length;r++)t[r]=e[r>>2]>>>r%4*8&255;return t}(function(e,t){const i=new Uint32Array(r(t)).fill(0);i.set(e),i[t>>5]|=128<<t%32,i[i.length-1]=t,e=i;let d=1732584193,p=-271733879,l=-1732584194,h=271733878;for(let t=0;t<e.length;t+=16){const r=d,i=p,u=l,m=h;d=a(d,p,l,h,e[t],7,-680876936),h=a(h,d,p,l,e[t+1],12,-389564586),l=a(l,h,d,p,e[t+2],17,606105819),p=a(p,l,h,d,e[t+3],22,-1044525330),d=a(d,p,l,h,e[t+4],7,-176418897),h=a(h,d,p,l,e[t+5],12,1200080426),l=a(l,h,d,p,e[t+6],17,-1473231341),p=a(p,l,h,d,e[t+7],22,-45705983),d=a(d,p,l,h,e[t+8],7,1770035416),h=a(h,d,p,l,e[t+9],12,-1958414417),l=a(l,h,d,p,e[t+10],17,-42063),p=a(p,l,h,d,e[t+11],22,-1990404162),d=a(d,p,l,h,e[t+12],7,1804603682),h=a(h,d,p,l,e[t+13],12,-40341101),l=a(l,h,d,p,e[t+14],17,-1502002290),p=a(p,l,h,d,e[t+15],22,1236535329),d=n(d,p,l,h,e[t+1],5,-165796510),h=n(h,d,p,l,e[t+6],9,-1069501632),l=n(l,h,d,p,e[t+11],14,643717713),p=n(p,l,h,d,e[t],20,-373897302),d=n(d,p,l,h,e[t+5],5,-701558691),h=n(h,d,p,l,e[t+10],9,38016083),l=n(l,h,d,p,e[t+15],14,-660478335),p=n(p,l,h,d,e[t+4],20,-405537848),d=n(d,p,l,h,e[t+9],5,568446438),h=n(h,d,p,l,e[t+14],9,-1019803690),l=n(l,h,d,p,e[t+3],14,-187363961),p=n(p,l,h,d,e[t+8],20,1163531501),d=n(d,p,l,h,e[t+13],5,-1444681467),h=n(h,d,p,l,e[t+2],9,-51403784),l=n(l,h,d,p,e[t+7],14,1735328473),p=n(p,l,h,d,e[t+12],20,-1926607734),d=o(d,p,l,h,e[t+5],4,-378558),h=o(h,d,p,l,e[t+8],11,-2022574463),l=o(l,h,d,p,e[t+11],16,1839030562),p=o(p,l,h,d,e[t+14],23,-35309556),d=o(d,p,l,h,e[t+1],4,-1530992060),h=o(h,d,p,l,e[t+4],11,1272893353),l=o(l,h,d,p,e[t+7],16,-155497632),p=o(p,l,h,d,e[t+10],23,-1094730640),d=o(d,p,l,h,e[t+13],4,681279174),h=o(h,d,p,l,e[t],11,-358537222),l=o(l,h,d,p,e[t+3],16,-722521979),p=o(p,l,h,d,e[t+6],23,76029189),d=o(d,p,l,h,e[t+9],4,-640364487),h=o(h,d,p,l,e[t+12],11,-421815835),l=o(l,h,d,p,e[t+15],16,530742520),p=o(p,l,h,d,e[t+2],23,-995338651),d=c(d,p,l,h,e[t],6,-198630844),h=c(h,d,p,l,e[t+7],10,1126891415),l=c(l,h,d,p,e[t+14],15,-1416354905),p=c(p,l,h,d,e[t+5],21,-57434055),d=c(d,p,l,h,e[t+12],6,1700485571),h=c(h,d,p,l,e[t+3],10,-1894986606),l=c(l,h,d,p,e[t+10],15,-1051523),p=c(p,l,h,d,e[t+1],21,-2054922799),d=c(d,p,l,h,e[t+8],6,1873313359),h=c(h,d,p,l,e[t+15],10,-30611744),l=c(l,h,d,p,e[t+6],15,-1560198380),p=c(p,l,h,d,e[t+13],21,1309151649),d=c(d,p,l,h,e[t+4],6,-145523070),h=c(h,d,p,l,e[t+11],10,-1120210379),l=c(l,h,d,p,e[t+2],15,718787259),p=c(p,l,h,d,e[t+9],21,-343485551),d=s(d,r),p=s(p,i),l=s(l,u),h=s(h,m)}return Uint32Array.of(d,p,l,h)}(function(e){if(0===e.length)return new Uint32Array;const t=new Uint32Array(r(8*e.length)).fill(0);for(let r=0;r<e.length;r++)t[r>>2]|=(255&e[r])<<r%4*8;return t}(e),8*e.length))}},591:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.URL=t.DNS=void 0;const s=r(338),i=r(2988);var a=r(2988);function n(e,t,r,a){return(0,i.default)(48,s.default,e,t,r,a)}Object.defineProperty(t,"DNS",{enumerable:!0,get:function(){return a.DNS}}),Object.defineProperty(t,"URL",{enumerable:!0,get:function(){return a.URL}}),n.DNS=i.DNS,n.URL=i.URL,t.default=n},736:(e,t,r)=>{e.exports=function(e){function t(e){let r,i,a,n=null;function o(...e){if(!o.enabled)return;const s=o,i=Number(new Date),a=i-(r||i);s.diff=a,s.prev=r,s.curr=i,r=i,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let n=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,(r,i)=>{if("%%"===r)return"%";n++;const a=t.formatters[i];if("function"==typeof a){const t=e[n];r=a.call(s,t),e.splice(n,1),n--}return r}),t.formatArgs.call(s,e),(s.log||t.log).apply(s,e)}return o.namespace=e,o.useColors=t.useColors(),o.color=t.selectColor(e),o.extend=s,o.destroy=t.destroy,Object.defineProperty(o,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==n?n:(i!==t.namespaces&&(i=t.namespaces,a=t.enabled(e)),a),set:e=>{n=e}}),"function"==typeof t.init&&t.init(o),o}function s(e,r){const s=t(this.namespace+(void 0===r?":":r)+e);return s.log=this.log,s}function i(e,t){let r=0,s=0,i=-1,a=0;for(;r<e.length;)if(s<t.length&&(t[s]===e[r]||"*"===t[s]))"*"===t[s]?(i=s,a=r,s++):(r++,s++);else{if(-1===i)return!1;s=i+1,a++,r=a}for(;s<t.length&&"*"===t[s];)s++;return s===t.length}return t.debug=t,t.default=t,t.coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){const e=[...t.names,...t.skips.map(e=>"-"+e)].join(",");return t.enable(""),e},t.enable=function(e){t.save(e),t.namespaces=e,t.names=[],t.skips=[];const r=("string"==typeof e?e:"").trim().replace(/\s+/g,",").split(",").filter(Boolean);for(const e of r)"-"===e[0]?t.skips.push(e.slice(1)):t.names.push(e)},t.enabled=function(e){for(const r of t.skips)if(i(e,r))return!1;for(const r of t.names)if(i(e,r))return!0;return!1},t.humanize=r(6585),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach(r=>{t[r]=e[r]}),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let r=0;for(let t=0;t<e.length;t++)r=(r<<5)-r+e.charCodeAt(t),r|=0;return t.colors[Math.abs(r)%t.colors.length]},t.enable(t.load()),t}},867:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ReactNative106=void 0;const s=r(7363),i=r(3953),a=r(2994),n=r(8046),o=r(4893),c=r(3303),d=r(1305),p=r(5544),l=r(5938),h=r(4256),u=new a.Logger("ReactNative106"),m="ReactNative106",f={OS:1024,MIS:1024};class g extends i.EnhancedEventEmitter{_closed=!1;_direction;_remoteSdp;_getSendExtendedRtpCapabilities;_forcedLocalDtlsRole;_pc;_mapMidTransceiver=new Map;_sendStream=new MediaStream;_hasDataChannelMediaSection=!1;_nextSendSctpStreamId=0;_transportReady=!1;static createFactory(){return{name:m,factory:e=>new g(e),getNativeRtpCapabilities:async()=>{u.debug("getNativeRtpCapabilities()");let e=new RTCPeerConnection({iceServers:[],iceTransportPolicy:"all",bundlePolicy:"max-bundle",rtcpMuxPolicy:"require"});try{e.addTransceiver("audio"),e.addTransceiver("video");const t=await e.createOffer();try{e.close()}catch(e){}e=void 0;const r=s.parse(t.sdp);return g.getLocalRtpCapabilities(r)}catch(t){try{e?.close()}catch(e){}throw e=void 0,t}},getNativeSctpCapabilities:async()=>(u.debug("getNativeSctpCapabilities()"),{numStreams:f})}}static getLocalRtpCapabilities(e){const t=p.extractRtpCapabilities({sdpObject:e});return h.addNackSupportForOpus(t),t}constructor({direction:e,iceParameters:t,iceCandidates:r,dtlsParameters:s,sctpParameters:i,iceServers:a,iceTransportPolicy:n,additionalSettings:o,getSendExtendedRtpCapabilities:c}){super(),u.debug("constructor()"),this._direction=e,this._remoteSdp=new d.RemoteSdp({iceParameters:t,iceCandidates:r,dtlsParameters:s,sctpParameters:i}),this._getSendExtendedRtpCapabilities=c,s.role&&"auto"!==s.role&&(this._forcedLocalDtlsRole="server"===s.role?"client":"server"),this._pc=new RTCPeerConnection({iceServers:a??[],iceTransportPolicy:n??"all",bundlePolicy:"max-bundle",rtcpMuxPolicy:"require",...o}),this._pc.addEventListener("icegatheringstatechange",this.onIceGatheringStateChange),this._pc.addEventListener("icecandidateerror",this.onIceCandidateError),this._pc.connectionState?this._pc.addEventListener("connectionstatechange",this.onConnectionStateChange):(u.warn("run() | pc.connectionState not supported, using pc.iceConnectionState"),this._pc.addEventListener("iceconnectionstatechange",this.onIceConnectionStateChange))}get name(){return m}close(){if(u.debug("close()"),!this._closed){this._closed=!0,this._sendStream.release(!1);try{this._pc.close()}catch(e){}this._pc.removeEventListener("icegatheringstatechange",this.onIceGatheringStateChange),this._pc.removeEventListener("icecandidateerror",this.onIceCandidateError),this._pc.removeEventListener("connectionstatechange",this.onConnectionStateChange),this._pc.removeEventListener("iceconnectionstatechange",this.onIceConnectionStateChange),this.emit("@close"),super.close()}}async updateIceServers(e){this.assertNotClosed(),u.debug("updateIceServers()");const t=this._pc.getConfiguration();t.iceServers=e,this._pc.setConfiguration(t)}async restartIce(e){if(this.assertNotClosed(),u.debug("restartIce()"),this._remoteSdp.updateIceParameters(e),this._transportReady)if("send"===this._direction){const e=await this._pc.createOffer({iceRestart:!0});u.debug("restartIce() | calling pc.setLocalDescription() [offer:%o]",e),await this._pc.setLocalDescription(e);const t={type:"answer",sdp:this._remoteSdp.getSdp()};u.debug("restartIce() | calling pc.setRemoteDescription() [answer:%o]",t),await this._pc.setRemoteDescription(t)}else{const e={type:"offer",sdp:this._remoteSdp.getSdp()};u.debug("restartIce() | calling pc.setRemoteDescription() [offer:%o]",e),await this._pc.setRemoteDescription(e);const t=await this._pc.createAnswer();u.debug("restartIce() | calling pc.setLocalDescription() [answer:%o]",t),await this._pc.setLocalDescription(t)}}async getTransportStats(){return this.assertNotClosed(),this._pc.getStats()}async send({track:e,encodings:t,codecOptions:r,codec:i,onRtpSender:a}){this.assertNotClosed(),this.assertSendDirection(),u.debug("send() [kind:%s, track.id:%s]",e.kind,e.id),t&&t.length>1&&t.forEach((e,t)=>{e.rid=`r${t}`});const o=this._remoteSdp.getNextMediaSectionIdx(),d=this._pc.addTransceiver(e,{direction:"sendonly",streams:[this._sendStream],sendEncodings:t});a&&a(d.sender);let h=await this._pc.createOffer(),m=s.parse(h.sdp);m.extmapAllowMixed&&this._remoteSdp.setSessionExtmapAllowMixed();const f=g.getLocalRtpCapabilities(m),_=this._getSendExtendedRtpCapabilities(f),b=n.getSendingRtpParameters(e.kind,_);b.codecs=n.reduceCodecs(b.codecs,i);const w=n.getSendingRemoteRtpParameters(e.kind,_);w.codecs=n.reduceCodecs(w.codecs,i),this._transportReady||await this.setupTransport({localDtlsRole:this._forcedLocalDtlsRole??"client",localSdpObject:m});let v=!1;const y=(0,c.parse)((t??[{}])[0].scalabilityMode);let S;t&&1===t.length&&y.spatialLayers>1&&"video/vp9"===b.codecs[0].mimeType.toLowerCase()&&(u.debug("send() | enabling legacy simulcast for VP9 SVC"),v=!0,m=s.parse(h.sdp),S=m.media[o.idx],l.addLegacySimulcast({offerMediaObject:S,numStreams:y.spatialLayers}),h={type:"offer",sdp:s.write(m)}),u.debug("send() | calling pc.setLocalDescription() [offer:%o]",h),await this._pc.setLocalDescription(h);let C=d.mid??void 0;if(C||u.warn("send() | missing transceiver.mid (bug in react-native-webrtc, using a workaround"),b.mid=C,m=s.parse(this._pc.localDescription.sdp),S=m.media[o.idx],b.rtcp.cname=p.getCname({offerMediaObject:S}),t)if(1===t.length){let e=l.getRtpEncodings({offerMediaObject:S});Object.assign(e[0],t[0]),v&&(e=[e[0]]),b.encodings=e}else b.encodings=t;else b.encodings=l.getRtpEncodings({offerMediaObject:S});if(b.encodings.length>1&&("video/vp8"===b.codecs[0].mimeType.toLowerCase()||"video/h264"===b.codecs[0].mimeType.toLowerCase()))for(const e of b.encodings)e.scalabilityMode?e.scalabilityMode=`L1T${y.temporalLayers}`:e.scalabilityMode="L1T3";this._remoteSdp.send({offerMediaObject:S,reuseMid:o.reuseMid,offerRtpParameters:b,answerRtpParameters:w,codecOptions:r});const R={type:"answer",sdp:this._remoteSdp.getSdp()};return u.debug("send() | calling pc.setRemoteDescription() [answer:%o]",R),await this._pc.setRemoteDescription(R),C||(C=d.mid,b.mid=C),this._mapMidTransceiver.set(C,d),{localId:C,rtpParameters:b,rtpSender:d.sender}}async stopSending(e){if(this.assertSendDirection(),this._closed)return;u.debug("stopSending() [localId:%s]",e);const t=this._mapMidTransceiver.get(e);if(!t)throw new Error("associated RTCRtpTransceiver not found");if(t.sender.replaceTrack(null),this._pc.removeTrack(t.sender),this._remoteSdp.closeMediaSection(t.mid))try{t.stop()}catch(e){}const r=await this._pc.createOffer();u.debug("stopSending() | calling pc.setLocalDescription() [offer:%o]",r),await this._pc.setLocalDescription(r);const s={type:"answer",sdp:this._remoteSdp.getSdp()};u.debug("stopSending() | calling pc.setRemoteDescription() [answer:%o]",s),await this._pc.setRemoteDescription(s),this._mapMidTransceiver.delete(e)}async pauseSending(e){this.assertNotClosed(),this.assertSendDirection(),u.debug("pauseSending() [localId:%s]",e);const t=this._mapMidTransceiver.get(e);if(!t)throw new Error("associated RTCRtpTransceiver not found");t.direction="inactive",this._remoteSdp.pauseMediaSection(e);const r=await this._pc.createOffer();u.debug("pauseSending() | calling pc.setLocalDescription() [offer:%o]",r),await this._pc.setLocalDescription(r);const s={type:"answer",sdp:this._remoteSdp.getSdp()};u.debug("pauseSending() | calling pc.setRemoteDescription() [answer:%o]",s),await this._pc.setRemoteDescription(s)}async resumeSending(e){this.assertNotClosed(),this.assertSendDirection(),u.debug("resumeSending() [localId:%s]",e);const t=this._mapMidTransceiver.get(e);if(this._remoteSdp.resumeSendingMediaSection(e),!t)throw new Error("associated RTCRtpTransceiver not found");t.direction="sendonly";const r=await this._pc.createOffer();u.debug("resumeSending() | calling pc.setLocalDescription() [offer:%o]",r),await this._pc.setLocalDescription(r);const s={type:"answer",sdp:this._remoteSdp.getSdp()};u.debug("resumeSending() | calling pc.setRemoteDescription() [answer:%o]",s),await this._pc.setRemoteDescription(s)}async replaceTrack(e,t){this.assertNotClosed(),this.assertSendDirection(),t?u.debug("replaceTrack() [localId:%s, track.id:%s]",e,t.id):u.debug("replaceTrack() [localId:%s, no track]",e);const r=this._mapMidTransceiver.get(e);if(!r)throw new Error("associated RTCRtpTransceiver not found");await r.sender.replaceTrack(t)}async setMaxSpatialLayer(e,t){this.assertNotClosed(),this.assertSendDirection(),u.debug("setMaxSpatialLayer() [localId:%s, spatialLayer:%s]",e,t);const r=this._mapMidTransceiver.get(e);if(!r)throw new Error("associated RTCRtpTransceiver not found");const s=r.sender.getParameters();s.encodings.forEach((e,r)=>{e.active=r<=t}),await r.sender.setParameters(s),this._remoteSdp.muxMediaSectionSimulcast(e,s.encodings);const i=await this._pc.createOffer();u.debug("setMaxSpatialLayer() | calling pc.setLocalDescription() [offer:%o]",i),await this._pc.setLocalDescription(i);const a={type:"answer",sdp:this._remoteSdp.getSdp()};u.debug("setMaxSpatialLayer() | calling pc.setRemoteDescription() [answer:%o]",a),await this._pc.setRemoteDescription(a)}async setRtpEncodingParameters(e,t){this.assertNotClosed(),this.assertSendDirection(),u.debug("setRtpEncodingParameters() [localId:%s, params:%o]",e,t);const r=this._mapMidTransceiver.get(e);if(!r)throw new Error("associated RTCRtpTransceiver not found");const s=r.sender.getParameters();s.encodings.forEach((e,r)=>{s.encodings[r]={...e,...t}}),await r.sender.setParameters(s),this._remoteSdp.muxMediaSectionSimulcast(e,s.encodings);const i=await this._pc.createOffer();u.debug("setRtpEncodingParameters() | calling pc.setLocalDescription() [offer:%o]",i),await this._pc.setLocalDescription(i);const a={type:"answer",sdp:this._remoteSdp.getSdp()};u.debug("setRtpEncodingParameters() | calling pc.setRemoteDescription() [answer:%o]",a),await this._pc.setRemoteDescription(a)}async getSenderStats(e){this.assertNotClosed(),this.assertSendDirection();const t=this._mapMidTransceiver.get(e);if(!t)throw new Error("associated RTCRtpTransceiver not found");return t.sender.getStats()}async sendDataChannel({ordered:e,maxPacketLifeTime:t,maxRetransmits:r,label:i,protocol:a}){this.assertNotClosed(),this.assertSendDirection();const n={negotiated:!0,id:this._nextSendSctpStreamId,ordered:e,maxPacketLifeTime:t,maxRetransmits:r,protocol:a};u.debug("sendDataChannel() [options:%o]",n);const o=this._pc.createDataChannel(i,n);if(this._nextSendSctpStreamId=++this._nextSendSctpStreamId%f.MIS,!this._hasDataChannelMediaSection){const e=await this._pc.createOffer(),t=s.parse(e.sdp),r=t.media.find(e=>"application"===e.type);this._transportReady||await this.setupTransport({localDtlsRole:this._forcedLocalDtlsRole??"client",localSdpObject:t}),u.debug("sendDataChannel() | calling pc.setLocalDescription() [offer:%o]",e),await this._pc.setLocalDescription(e),this._remoteSdp.sendSctpAssociation({offerMediaObject:r});const i={type:"answer",sdp:this._remoteSdp.getSdp()};u.debug("sendDataChannel() | calling pc.setRemoteDescription() [answer:%o]",i),await this._pc.setRemoteDescription(i),this._hasDataChannelMediaSection=!0}return{dataChannel:o,sctpStreamParameters:{streamId:n.id,ordered:n.ordered,maxPacketLifeTime:n.maxPacketLifeTime,maxRetransmits:n.maxRetransmits}}}async receive(e){this.assertNotClosed(),this.assertRecvDirection();const t=[],r=new Map;for(const t of e){const{trackId:e,kind:s,rtpParameters:i,streamId:a}=t;u.debug("receive() [trackId:%s, kind:%s]",e,s);const n=i.mid??String(this._mapMidTransceiver.size);r.set(e,n),this._remoteSdp.receive({mid:n,kind:s,offerRtpParameters:i,streamId:a??i.rtcp.cname,trackId:e})}const i={type:"offer",sdp:this._remoteSdp.getSdp()};u.debug("receive() | calling pc.setRemoteDescription() [offer:%o]",i),await this._pc.setRemoteDescription(i);for(const t of e){const{trackId:e,onRtpReceiver:s}=t;if(s){const t=r.get(e),i=this._pc.getTransceivers().find(e=>e.mid===t);if(!i)throw new Error("transceiver not found");s(i.receiver)}}let a=await this._pc.createAnswer();const n=s.parse(a.sdp);for(const t of e){const{trackId:e,rtpParameters:s}=t,i=r.get(e),a=n.media.find(e=>String(e.mid)===i);p.applyCodecParameters({offerRtpParameters:s,answerMediaObject:a})}a={type:"answer",sdp:s.write(n)},this._transportReady||await this.setupTransport({localDtlsRole:this._forcedLocalDtlsRole??"client",localSdpObject:n}),u.debug("receive() | calling pc.setLocalDescription() [answer:%o]",a),await this._pc.setLocalDescription(a);for(const s of e){const{trackId:e}=s,i=r.get(e),a=this._pc.getTransceivers().find(e=>e.mid===i);if(!a)throw new Error("new RTCRtpTransceiver not found");this._mapMidTransceiver.set(i,a),t.push({localId:i,track:a.receiver.track,rtpReceiver:a.receiver})}return t}async stopReceiving(e){if(this.assertRecvDirection(),this._closed)return;for(const t of e){u.debug("stopReceiving() [localId:%s]",t);const e=this._mapMidTransceiver.get(t);if(!e)throw new Error("associated RTCRtpTransceiver not found");this._remoteSdp.closeMediaSection(e.mid)}const t={type:"offer",sdp:this._remoteSdp.getSdp()};u.debug("stopReceiving() | calling pc.setRemoteDescription() [offer:%o]",t),await this._pc.setRemoteDescription(t);const r=await this._pc.createAnswer();u.debug("stopReceiving() | calling pc.setLocalDescription() [answer:%o]",r),await this._pc.setLocalDescription(r);for(const t of e)this._mapMidTransceiver.delete(t)}async pauseReceiving(e){this.assertNotClosed(),this.assertRecvDirection();for(const t of e){u.debug("pauseReceiving() [localId:%s]",t);const e=this._mapMidTransceiver.get(t);if(!e)throw new Error("associated RTCRtpTransceiver not found");e.direction="inactive",this._remoteSdp.pauseMediaSection(t)}const t={type:"offer",sdp:this._remoteSdp.getSdp()};u.debug("pauseReceiving() | calling pc.setRemoteDescription() [offer:%o]",t),await this._pc.setRemoteDescription(t);const r=await this._pc.createAnswer();u.debug("pauseReceiving() | calling pc.setLocalDescription() [answer:%o]",r),await this._pc.setLocalDescription(r)}async resumeReceiving(e){this.assertNotClosed(),this.assertRecvDirection();for(const t of e){u.debug("resumeReceiving() [localId:%s]",t);const e=this._mapMidTransceiver.get(t);if(!e)throw new Error("associated RTCRtpTransceiver not found");e.direction="recvonly",this._remoteSdp.resumeReceivingMediaSection(t)}const t={type:"offer",sdp:this._remoteSdp.getSdp()};u.debug("resumeReceiving() | calling pc.setRemoteDescription() [offer:%o]",t),await this._pc.setRemoteDescription(t);const r=await this._pc.createAnswer();u.debug("resumeReceiving() | calling pc.setLocalDescription() [answer:%o]",r),await this._pc.setLocalDescription(r)}async getReceiverStats(e){this.assertNotClosed(),this.assertRecvDirection();const t=this._mapMidTransceiver.get(e);if(!t)throw new Error("associated RTCRtpTransceiver not found");return t.receiver.getStats()}async receiveDataChannel({sctpStreamParameters:e,label:t,protocol:r}){this.assertNotClosed(),this.assertRecvDirection();const{streamId:i,ordered:a,maxPacketLifeTime:n,maxRetransmits:o}=e,c={negotiated:!0,id:i,ordered:a,maxPacketLifeTime:n,maxRetransmits:o,protocol:r};u.debug("receiveDataChannel() [options:%o]",c);const d=this._pc.createDataChannel(t,c);if(!this._hasDataChannelMediaSection){this._remoteSdp.receiveSctpAssociation();const e={type:"offer",sdp:this._remoteSdp.getSdp()};u.debug("receiveDataChannel() | calling pc.setRemoteDescription() [offer:%o]",e),await this._pc.setRemoteDescription(e);const t=await this._pc.createAnswer();if(!this._transportReady){const e=s.parse(t.sdp);await this.setupTransport({localDtlsRole:this._forcedLocalDtlsRole??"client",localSdpObject:e})}u.debug("receiveDataChannel() | calling pc.setRemoteDescription() [answer:%o]",t),await this._pc.setLocalDescription(t),this._hasDataChannelMediaSection=!0}return{dataChannel:d}}async setupTransport({localDtlsRole:e,localSdpObject:t}){t||(t=s.parse(this._pc.localDescription.sdp));const r=p.extractDtlsParameters({sdpObject:t});r.role=e,this._remoteSdp.updateDtlsRole("client"===e?"server":"client"),await new Promise((e,t)=>{this.safeEmit("@connect",{dtlsParameters:r},e,t)}),this._transportReady=!0}onIceGatheringStateChange=()=>{this.emit("@icegatheringstatechange",this._pc.iceGatheringState)};onIceCandidateError=e=>{this.emit("@icecandidateerror",e)};onConnectionStateChange=()=>{this.emit("@connectionstatechange",this._pc.connectionState)};onIceConnectionStateChange=()=>{switch(this._pc.iceConnectionState){case"checking":this.emit("@connectionstatechange","connecting");break;case"connected":case"completed":this.emit("@connectionstatechange","connected");break;case"failed":this.emit("@connectionstatechange","failed");break;case"disconnected":this.emit("@connectionstatechange","disconnected");break;case"closed":this.emit("@connectionstatechange","closed")}};assertNotClosed(){if(this._closed)throw new o.InvalidStateError("method called in a closed handler")}assertSendDirection(){if("send"!==this._direction)throw new Error('method can just be called for handlers with "send" direction')}assertRecvDirection(){if("recv"!==this._direction)throw new Error('method can just be called for handlers with "recv" direction')}}t.ReactNative106=g},1305:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.RemoteSdp=void 0;const s=r(7363),i=r(2994),a=r(3471),n=["av1","h264"],o=new i.Logger("RemoteSdp");t.RemoteSdp=class{_iceParameters;_iceCandidates;_dtlsParameters;_sctpParameters;_plainRtpParameters;_mediaSections=[];_midToIndex=new Map;_firstMid;_sdpObject;constructor({iceParameters:e,iceCandidates:t,dtlsParameters:r,sctpParameters:s,plainRtpParameters:i}){if(this._iceParameters=e,this._iceCandidates=t,this._dtlsParameters=r,this._sctpParameters=s,this._plainRtpParameters=i,this._sdpObject={version:0,origin:{address:"0.0.0.0",ipVer:4,netType:"IN",sessionId:"10000",sessionVersion:0,username:"mediasoup-client"},name:"-",timing:{start:0,stop:0},media:[]},e?.iceLite&&(this._sdpObject.icelite="ice-lite"),r){this._sdpObject.msidSemantic={semantic:"WMS",token:"*"};const e=this._dtlsParameters.fingerprints.length;this._sdpObject.fingerprint={type:r.fingerprints[e-1].algorithm,hash:r.fingerprints[e-1].value},this._sdpObject.groups=[{type:"BUNDLE",mids:""}]}i&&(this._sdpObject.origin.address=i.ip,this._sdpObject.origin.ipVer=i.ipVersion)}updateIceParameters(e){o.debug("updateIceParameters() [iceParameters:%o]",e),this._iceParameters=e,this._sdpObject.icelite=e.iceLite?"ice-lite":void 0;for(const t of this._mediaSections)t.setIceParameters(e)}updateDtlsRole(e){o.debug("updateDtlsRole() [role:%s]",e),this._dtlsParameters.role=e;for(const t of this._mediaSections)t.setDtlsRole(e)}setSessionExtmapAllowMixed(){o.debug("setSessionExtmapAllowMixed()"),this._sdpObject.extmapAllowMixed="extmap-allow-mixed"}getNextMediaSectionIdx(){for(let e=0;e<this._mediaSections.length;++e){const t=this._mediaSections[e];if(t.closed)return{idx:e,reuseMid:t.mid}}return{idx:this._mediaSections.length}}send({offerMediaObject:e,reuseMid:t,offerRtpParameters:r,answerRtpParameters:s,codecOptions:i}){const o=new a.AnswerMediaSection({iceParameters:this._iceParameters,iceCandidates:this._iceCandidates,dtlsParameters:this._dtlsParameters,plainRtpParameters:this._plainRtpParameters,offerMediaObject:e,offerRtpParameters:r,answerRtpParameters:s,codecOptions:i}),c=o.getObject();c.rtp.find(e=>n.includes(e.codec.toLowerCase()))||(c.ext=c.ext?.filter(e=>"https://aomediacodec.github.io/av1-rtp-spec/#dependency-descriptor-rtp-header-extension"!==e.uri)),t?this._replaceMediaSection(o,t):this._midToIndex.has(o.mid)?this._replaceMediaSection(o):this._addMediaSection(o)}receive({mid:e,kind:t,offerRtpParameters:r,streamId:s,trackId:i}){this.setSessionExtmapAllowMixed();const n=new a.OfferMediaSection({iceParameters:this._iceParameters,iceCandidates:this._iceCandidates,dtlsParameters:this._dtlsParameters,plainRtpParameters:this._plainRtpParameters,mid:e,kind:t,offerRtpParameters:r,streamId:s,trackId:i}),o=this._mediaSections.find(e=>e.closed);o?this._replaceMediaSection(n,o.mid):this._addMediaSection(n)}pauseMediaSection(e){this._findMediaSection(e).pause()}resumeSendingMediaSection(e){this._findMediaSection(e).resume()}resumeReceivingMediaSection(e){this._findMediaSection(e).resume()}disableMediaSection(e){this._findMediaSection(e).disable()}closeMediaSection(e){const t=this._findMediaSection(e);return e===this._firstMid?(o.debug("closeMediaSection() | cannot close first media section, disabling it instead [mid:%s]",e),this.disableMediaSection(e),!1):(t.close(),this._regenerateBundleMids(),!0)}muxMediaSectionSimulcast(e,t){const r=this._findMediaSection(e);r.muxSimulcastStreams(t),this._replaceMediaSection(r)}sendSctpAssociation({offerMediaObject:e}){const t=new a.AnswerMediaSection({iceParameters:this._iceParameters,iceCandidates:this._iceCandidates,dtlsParameters:this._dtlsParameters,sctpParameters:this._sctpParameters,plainRtpParameters:this._plainRtpParameters,offerMediaObject:e});this._addMediaSection(t)}receiveSctpAssociation(){const e=new a.OfferMediaSection({iceParameters:this._iceParameters,iceCandidates:this._iceCandidates,dtlsParameters:this._dtlsParameters,sctpParameters:this._sctpParameters,plainRtpParameters:this._plainRtpParameters,mid:"datachannel",kind:"application"});this._addMediaSection(e)}getSdp(){return this._sdpObject.origin.sessionVersion++,s.write(this._sdpObject)}_addMediaSection(e){this._firstMid||(this._firstMid=e.mid),this._mediaSections.push(e),this._midToIndex.set(e.mid,this._mediaSections.length-1),this._sdpObject.media.push(e.getObject()),this._regenerateBundleMids()}_replaceMediaSection(e,t){if("string"==typeof t){const r=this._midToIndex.get(t);if(void 0===r)throw new Error(`no media section found for reuseMid '${t}'`);const s=this._mediaSections[r];this._mediaSections[r]=e,this._midToIndex.delete(s.mid),this._midToIndex.set(e.mid,r),this._sdpObject.media[r]=e.getObject(),this._regenerateBundleMids()}else{const t=this._midToIndex.get(e.mid);if(void 0===t)throw new Error(`no media section found with mid '${e.mid}'`);this._mediaSections[t]=e,this._sdpObject.media[t]=e.getObject()}}_findMediaSection(e){const t=this._midToIndex.get(e);if(void 0===t)throw new Error(`no media section found with mid '${e}'`);return this._mediaSections[t]}_regenerateBundleMids(){this._dtlsParameters&&(this._sdpObject.groups[0].mids=this._mediaSections.filter(e=>!e.closed).map(e=>e.mid).join(" "))}}},1425:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.updateV1State=void 0;const s=r(2291),i=r(6011),a={};function n(e,t,r){return e.msecs??=-1/0,e.nsecs??=0,t===e.msecs?(e.nsecs++,e.nsecs>=1e4&&(e.node=void 0,e.nsecs=0)):t>e.msecs?e.nsecs=0:t<e.msecs&&(e.node=void 0),e.node||(e.node=r.slice(10,16),e.node[0]|=1,e.clockseq=16383&(r[8]<<8|r[9])),e.msecs=t,e}function o(e,t,r,s,i,a,n=0){if(e.length<16)throw new Error("Random bytes length must be >= 16");if(a){if(n<0||n+16>a.length)throw new RangeError(`UUID byte range ${n}:${n+15} is out of buffer bounds`)}else a=new Uint8Array(16),n=0;t??=Date.now(),r??=0,s??=16383&(e[8]<<8|e[9]),i??=e.slice(10,16);const o=(1e4*(268435455&(t+=122192928e5))+r)%4294967296;a[n++]=o>>>24&255,a[n++]=o>>>16&255,a[n++]=o>>>8&255,a[n++]=255&o;const c=t/4294967296*1e4&268435455;a[n++]=c>>>8&255,a[n++]=255&c,a[n++]=c>>>24&15|16,a[n++]=c>>>16&255,a[n++]=s>>>8|128,a[n++]=255&s;for(let e=0;e<6;++e)a[n++]=i[e];return a}t.updateV1State=n,t.default=function(e,t,r){let c;const d=e?._v6??!1;if(e){const t=Object.keys(e);1===t.length&&"_v6"===t[0]&&(e=void 0)}if(e)c=o(e.random??e.rng?.()??(0,s.default)(),e.msecs,e.nsecs,e.clockseq,e.node,t,r);else{const e=Date.now(),i=(0,s.default)();n(a,e,i),c=o(i,a.msecs,a.nsecs,d?void 0:a.clockseq,d?void 0:a.node,t,r)}return t??(0,i.unsafeStringify)(c)}},1765:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.clone=function(e){return void 0===e?void 0:Number.isNaN(e)?NaN:"function"==typeof structuredClone?structuredClone(e):JSON.parse(JSON.stringify(e))},t.generateRandomNumber=function(){return Math.round(1e7*Math.random())},t.deepFreeze=function e(t){const r=Reflect.ownKeys(t);for(const s of r){const r=t[s];(r&&"object"==typeof r||"function"==typeof r)&&e(r)}return Object.freeze(t)}},1797:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const s=r(9746);t.default=function(e){if(!(0,s.default)(e))throw TypeError("Invalid UUID");let t;return Uint8Array.of((t=parseInt(e.slice(0,8),16))>>>24,t>>>16&255,t>>>8&255,255&t,(t=parseInt(e.slice(9,13),16))>>>8,255&t,(t=parseInt(e.slice(14,18),16))>>>8,255&t,(t=parseInt(e.slice(19,23),16))>>>8,255&t,(t=parseInt(e.slice(24,36),16))/1099511627776&255,t/4294967296&255,t>>>24&255,t>>>16&255,t>>>8&255,255&t)}},2109:function(e,t,r){var s;!function(i,a){"use strict";var n,o="user-agent",c="",d="function",p="undefined",l="object",h="string",u="browser",m="cpu",f="device",g="engine",_="os",b="result",w="name",v="type",y="vendor",S="version",C="architecture",R="major",k="model",T="console",P="mobile",x="tablet",E="smarttv",D="wearable",L="xr",I="embedded",M="inapp",O="brands",j="formFactors",F="fullVersionList",A="platform",N="platformVersion",z="bitness",$="sec-ch-ua",U=$+"-full-version-list",B=$+"-arch",q=$+"-"+z,H=$+"-form-factors",V=$+"-"+P,G=$+"-"+k,Q=$+"-"+A,W=Q+"-version",K=[O,F,P,k,A,N,C,j,z],X="Amazon",J="Apple",Y="ASUS",Z="BlackBerry",ee="Google",te="Huawei",re="Lenovo",se="Honor",ie="LG",ae="Microsoft",ne="Motorola",oe="Nvidia",ce="OnePlus",de="OPPO",pe="Samsung",le="Sharp",he="Sony",ue="Xiaomi",me="Zebra",fe="Chrome",ge="Chromium",_e="Chromecast",be="Edge",we="Firefox",ve="Opera",ye="Facebook",Se="Sogou",Ce="Mobile ",Re=" Browser",ke="Windows",Te=typeof i!==p,Pe=Te&&i.navigator?i.navigator:a,xe=Pe&&Pe.userAgentData?Pe.userAgentData:a,Ee=function(e){for(var t={},r=0;r<e.length;r++)t[e[r].toUpperCase()]=e[r];return t},De=function(e,t){if(typeof e===l&&e.length>0){for(var r in e)if(Oe(t)==Oe(e[r]))return!0;return!1}return!!Ie(e)&&Oe(t)==Oe(e)},Le=function(e,t){for(var r in e)return/^(browser|cpu|device|engine|os)$/.test(r)||!!t&&Le(e[r])},Ie=function(e){return typeof e===h},Me=function(e){if(!e)return a;for(var t=[],r=Ae(/\\?\"/g,e).split(","),s=0;s<r.length;s++)if(r[s].indexOf(";")>-1){var i=ze(r[s]).split(";v=");t[s]={brand:i[0],version:i[1]}}else t[s]=ze(r[s]);return t},Oe=function(e){return Ie(e)?e.toLowerCase():e},je=function(e){return Ie(e)?Ae(/[^\d\.]/g,e).split(".")[0]:a},Fe=function(e){for(var t in e){var r=e[t];typeof r==l&&2==r.length?this[r[0]]=r[1]:this[r]=a}return this},Ae=function(e,t){return Ie(t)?t.replace(e,c):t},Ne=function(e){return Ae(/\\?\"/g,e)},ze=function(e,t){if(Ie(e))return e=Ae(/^\s\s*/,e),typeof t===p?e:e.substring(0,500)},$e=function(e,t){if(e&&t)for(var r,s,i,n,o,c,p=0;p<t.length&&!o;){var h=t[p],u=t[p+1];for(r=s=0;r<h.length&&!o&&h[r];)if(o=h[r++].exec(e))for(i=0;i<u.length;i++)c=o[++s],typeof(n=u[i])===l&&n.length>0?2===n.length?typeof n[1]==d?this[n[0]]=n[1].call(this,c):this[n[0]]=n[1]:n.length>=3&&(typeof n[1]!==d||n[1].exec&&n[1].test?3==n.length?this[n[0]]=c?c.replace(n[1],n[2]):a:4==n.length?this[n[0]]=c?n[3].call(this,c.replace(n[1],n[2])):a:n.length>4&&(this[n[0]]=c?n[3].apply(this,[c.replace(n[1],n[2])].concat(n.slice(4))):a):n.length>3?this[n[0]]=c?n[1].apply(this,n.slice(2)):a:this[n[0]]=c?n[1].call(this,c,n[2]):a):this[n]=c||a;p+=2}},Ue=function(e,t){for(var r in t)if(typeof t[r]===l&&t[r].length>0){for(var s=0;s<t[r].length;s++)if(De(t[r][s],e))return"?"===r?a:r}else if(De(t[r],e))return"?"===r?a:r;return t.hasOwnProperty("*")?t["*"]:e},Be={ME:"4.90","NT 3.51":"3.51","NT 4.0":"4.0",2e3:["5.0","5.01"],XP:["5.1","5.2"],Vista:"6.0",7:"6.1",8:"6.2",8.1:"6.3",10:["6.4","10.0"],NT:""},qe={embedded:"Automotive",mobile:"Mobile",tablet:["Tablet","EInk"],smarttv:"TV",wearable:"Watch",xr:["VR","XR"],"?":["Desktop","Unknown"],"*":a},He={Chrome:"Google Chrome",Edge:"Microsoft Edge","Edge WebView2":"Microsoft Edge WebView2","Chrome WebView":"Android WebView","Chrome Headless":"HeadlessChrome","Huawei Browser":"HuaweiBrowser","MIUI Browser":"Miui Browser","Opera Mobi":"OperaMobile",Yandex:"YaBrowser"},Ve={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[S,[w,Ce+"Chrome"]],[/webview.+edge\/([\w\.]+)/i],[S,[w,be+" WebView"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[S,[w,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[w,S],[/opios[\/ ]+([\w\.]+)/i],[S,[w,ve+" Mini"]],[/\bop(?:rg)?x\/([\w\.]+)/i],[S,[w,ve+" GX"]],[/\bopr\/([\w\.]+)/i],[S,[w,ve]],[/\bb[ai]*d(?:uhd|[ub]*[aekoprswx]{5,6})[\/ ]?([\w\.]+)/i],[S,[w,"Baidu"]],[/\b(?:mxbrowser|mxios|myie2)\/?([-\w\.]*)\b/i],[S,[w,"Maxthon"]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer|sleipnir)[\/ ]?([\w\.]*)/i,/(avant|iemobile|slim(?:browser|boat|jet))[\/ ]?([\d\.]*)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|ovibrowser|bolt|iron|vivaldi|iridium|phantomjs|bowser|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|duckduckgo|klar|helio|(?=comodo_)?dragon|otter|dooble|(?:lg |qute)browser)\/([-\w\.]+)/i,/(heytap|ovi|115|surf)browser\/([\d\.]+)/i,/(ecosia|weibo)(?:__| \w+@)([\d\.]+)/i],[w,S],[/quark(?:pc)?\/([-\w\.]+)/i],[S,[w,"Quark"]],[/\bddg\/([\w\.]+)/i],[S,[w,"DuckDuckGo"]],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[S,[w,"UCBrowser"]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i,/micromessenger\/([\w\.]+)/i],[S,[w,"WeChat"]],[/konqueror\/([\w\.]+)/i],[S,[w,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[S,[w,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[S,[w,"Yandex"]],[/slbrowser\/([\w\.]+)/i],[S,[w,"Smart "+re+Re]],[/(avast|avg)\/([\w\.]+)/i],[[w,/(.+)/,"$1 Secure"+Re],S],[/\bfocus\/([\w\.]+)/i],[S,[w,we+" Focus"]],[/\bopt\/([\w\.]+)/i],[S,[w,ve+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[S,[w,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[S,[w,"Dolphin"]],[/coast\/([\w\.]+)/i],[S,[w,ve+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[S,[w,"MIUI"+Re]],[/fxios\/([\w\.-]+)/i],[S,[w,Ce+we]],[/\bqihoobrowser\/?([\w\.]*)/i],[S,[w,"360"]],[/\b(qq)\/([\w\.]+)/i],[[w,/(.+)/,"$1Browser"],S],[/(oculus|sailfish|huawei|vivo|pico)browser\/([\w\.]+)/i],[[w,/(.+)/,"$1"+Re],S],[/samsungbrowser\/([\w\.]+)/i],[S,[w,pe+" Internet"]],[/metasr[\/ ]?([\d\.]+)/i],[S,[w,Se+" Explorer"]],[/(sogou)mo\w+\/([\d\.]+)/i],[[w,Se+" Mobile"],S],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|2345(?=browser|chrome|explorer))\w*[\/ ]?v?([\w\.]+)/i],[w,S],[/(lbbrowser|rekonq)/i],[w],[/ome\/([\w\.]+) \w* ?(iron) saf/i,/ome\/([\w\.]+).+qihu (360)[es]e/i],[S,w],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[w,ye],S,[v,M]],[/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/(daum)apps[\/ ]([\w\.]+)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(alipay)client\/([\w\.]+)/i,/(twitter)(?:and| f.+e\/([\w\.]+))/i,/(instagram|snapchat|klarna)[\/ ]([-\w\.]+)/i],[w,S,[v,M]],[/\bgsa\/([\w\.]+) .*safari\//i],[S,[w,"GSA"],[v,M]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[S,[w,"TikTok"],[v,M]],[/\[(linkedin)app\]/i],[w,[v,M]],[/(chromium)[\/ ]([-\w\.]+)/i],[w,S],[/headlesschrome(?:\/([\w\.]+)| )/i],[S,[w,fe+" Headless"]],[/wv\).+chrome\/([\w\.]+).+edgw\//i],[S,[w,be+" WebView2"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[w,fe+" WebView"],S],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[S,[w,"Android"+Re]],[/chrome\/([\w\.]+) mobile/i],[S,[w,Ce+"Chrome"]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[w,S],[/version\/([\w\.\,]+) .*mobile(?:\/\w+ | ?)safari/i],[S,[w,Ce+"Safari"]],[/iphone .*mobile(?:\/\w+ | ?)safari/i],[[w,Ce+"Safari"]],[/version\/([\w\.\,]+) .*(safari)/i],[S,w],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[w,[S,"1"]],[/(webkit|khtml)\/([\w\.]+)/i],[w,S],[/(?:mobile|tablet);.*(firefox)\/([\w\.-]+)/i],[[w,Ce+we],S],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[w,"Netscape"],S],[/(wolvic|librewolf)\/([\w\.]+)/i],[w,S],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[S,[w,we+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(amaya|dillo|doris|icab|ladybird|lynx|mosaic|netsurf|obigo|polaris|w3m|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/\b(links) \(([\w\.]+)/i],[w,[S,/_/g,"."]],[/(cobalt)\/([\w\.]+)/i],[w,[S,/[^\d\.]+./,c]]],cpu:[[/\b((amd|x|x86[-_]?|wow|win)64)\b/i],[[C,"amd64"]],[/(ia32(?=;))/i,/\b((i[346]|x)86)(pc)?\b/i],[[C,"ia32"]],[/\b(aarch64|arm(v?[89]e?l?|_?64))\b/i],[[C,"arm64"]],[/\b(arm(v[67])?ht?n?[fl]p?)\b/i],[[C,"armhf"]],[/( (ce|mobile); ppc;|\/[\w\.]+arm\b)/i],[[C,"arm"]],[/((ppc|powerpc)(64)?)( mac|;|\))/i],[[C,/ower/,c,Oe]],[/ sun4\w[;\)]/i],[[C,"sparc"]],[/\b(avr32|ia64(?=;)|68k(?=\))|\barm(?=v([1-7]|[5-7]1)l?|;|eabi)|(irix|mips|sparc)(64)?\b|pa-risc)/i],[[C,Oe]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[k,[y,pe],[v,x]],[/\b((?:s[cgp]h|gt|sm)-(?![lr])\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]((?!sm-[lr]|browser)[-\w]+)/i,/sec-(sgh\w+)/i],[k,[y,pe],[v,P]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[k,[y,J],[v,P]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[k,[y,J],[v,x]],[/(macintosh);/i],[k,[y,J]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[k,[y,le],[v,P]],[/\b((?:brt|eln|hey2?|gdi|jdn)-a?[lnw]09|(?:ag[rm]3?|jdn2|kob2)-a?[lw]0[09]hn)(?: bui|\)|;)/i],[k,[y,se],[v,x]],[/honor([-\w ]+)[;\)]/i],[k,[y,se],[v,P]],[/\b((?:ag[rs][2356]?k?|bah[234]?|bg[2o]|bt[kv]|cmr|cpn|db[ry]2?|jdn2|got|kob2?k?|mon|pce|scm|sht?|[tw]gr|vrd)-[ad]?[lw][0125][09]b?|605hw|bg2-u03|(?:gem|fdr|m2|ple|t1)-[7a]0[1-4][lu]|t1-a2[13][lw]|mediapad[\w\. ]*(?= bui|\)))\b(?!.+d\/s)/i],[k,[y,te],[v,x]],[/(?:huawei)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[k,[y,te],[v,P]],[/oid[^\)]+; (2[\dbc]{4}(182|283|rp\w{2})[cgl]|m2105k81a?c)(?: bui|\))/i,/\b((?:red)?mi[-_ ]?pad[\w- ]*)(?: bui|\))/i],[[k,/_/g," "],[y,ue],[v,x]],[/\b(poco[\w ]+|m2\d{3}j\d\d[a-z]{2})(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/oid[^\)]+; (m?[12][0-389][01]\w{3,6}[c-y])( bui|; wv|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite|pro)?)(?: bui|\))/i,/ ([\w ]+) miui\/v?\d/i],[[k,/_/g," "],[y,ue],[v,P]],[/droid.+; (cph2[3-6]\d[13579]|((gm|hd)19|(ac|be|in|kb)20|(d[en]|eb|le|mt)21|ne22)[0-2]\d|p[g-k]\w[1m]10)\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[k,[y,ce],[v,P]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[k,[y,de],[v,P]],[/\b(opd2(\d{3}a?))(?: bui|\))/i],[k,[y,Ue,{OnePlus:["203","304","403","404","413","415"],"*":de}],[v,x]],[/(vivo (5r?|6|8l?|go|one|s|x[il]?[2-4]?)[\w\+ ]*)(?: bui|\))/i],[k,[y,"BLU"],[v,P]],[/; vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[k,[y,"Vivo"],[v,P]],[/\b(rmx[1-3]\d{3})(?: bui|;|\))/i],[k,[y,"Realme"],[v,P]],[/(ideatab[-\w ]+|602lv|d-42a|a101lv|a2109a|a3500-hv|s[56]000|pb-6505[my]|tb-?x?\d{3,4}(?:f[cu]|xu|[av])|yt\d?-[jx]?\d+[lfmx])( bui|;|\)|\/)/i,/lenovo ?(b[68]0[08]0-?[hf]?|tab(?:[\w- ]+?)|tb[\w-]{6,7})( bui|;|\)|\/)/i],[k,[y,re],[v,x]],[/lenovo[-_ ]?([-\w ]+?)(?: bui|\)|\/)/i],[k,[y,re],[v,P]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ]([\w\s]+)(\)| bui)/i,/((?:moto(?! 360)[-\w\(\) ]+|xt\d{3,4}[cgkosw\+]?[-\d]*|nexus 6)(?= bui|\)))/i],[k,[y,ne],[v,P]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[k,[y,ne],[v,x]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[k,[y,ie],[v,x]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+(?!.*(?:browser|netcast|android tv|watch|webos))(\w+)/i,/\blg-?([\d\w]+) bui/i],[k,[y,ie],[v,P]],[/(nokia) (t[12][01])/i],[y,k,[v,x]],[/(?:maemo|nokia).*(n900|lumia \d+|rm-\d+)/i,/nokia[-_ ]?(([-\w\. ]*))/i],[[k,/_/g," "],[v,P],[y,"Nokia"]],[/(pixel (c|tablet))\b/i],[k,[y,ee],[v,x]],[/droid.+;(?: google)? (g(01[13]a|020[aem]|025[jn]|1b60|1f8f|2ybb|4s1m|576d|5nz6|8hhn|8vou|a02099|c15s|d1yq|e2ae|ec77|gh2x|kv4x|p4bc|pj41|r83y|tt9q|ur25|wvk6)|pixel[\d ]*a?( pro)?( xl)?( fold)?( \(5g\))?)( bui|\))/i],[k,[y,ee],[v,P]],[/(google) (pixelbook( go)?)/i],[y,k],[/droid.+; (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-\w\w\d\d)(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[k,[y,he],[v,P]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[k,"Xperia Tablet"],[y,he],[v,x]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo(?!bc)\w\w)( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[k,[y,X],[v,x]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[k,/(.+)/g,"Fire Phone $1"],[y,X],[v,P]],[/(playbook);[-\w\),; ]+(rim)/i],[k,y,[v,x]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[k,[y,Z],[v,P]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[k,[y,Y],[v,x]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[k,[y,Y],[v,P]],[/(nexus 9)/i],[k,[y,"HTC"],[v,x]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[y,[k,/_/g," "],[v,P]],[/tcl (xess p17aa)/i,/droid [\w\.]+; ((?:8[14]9[16]|9(?:0(?:48|60|8[01])|1(?:3[27]|66)|2(?:6[69]|9[56])|466))[gqswx])(_\w(\w|\w\w))?(\)| bui)/i],[k,[y,"TCL"],[v,x]],[/droid [\w\.]+; (418(?:7d|8v)|5087z|5102l|61(?:02[dh]|25[adfh]|27[ai]|56[dh]|59k|65[ah])|a509dl|t(?:43(?:0w|1[adepqu])|50(?:6d|7[adju])|6(?:09dl|10k|12b|71[efho]|76[hjk])|7(?:66[ahju]|67[hw]|7[045][bh]|71[hk]|73o|76[ho]|79w|81[hks]?|82h|90[bhsy]|99b)|810[hs]))(_\w(\w|\w\w))?(\)| bui)/i],[k,[y,"TCL"],[v,P]],[/(itel) ((\w+))/i],[[y,Oe],k,[v,Ue,{tablet:["p10001l","w7001"],"*":"mobile"}]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[k,[y,"Acer"],[v,x]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[k,[y,"Meizu"],[v,P]],[/; ((?:power )?armor(?:[\w ]{0,8}))(?: bui|\))/i],[k,[y,"Ulefone"],[v,P]],[/; (energy ?\w+)(?: bui|\))/i,/; energizer ([\w ]+)(?: bui|\))/i],[k,[y,"Energizer"],[v,P]],[/; cat (b35);/i,/; (b15q?|s22 flip|s48c|s62 pro)(?: bui|\))/i],[k,[y,"Cat"],[v,P]],[/((?:new )?andromax[\w- ]+)(?: bui|\))/i],[k,[y,"Smartfren"],[v,P]],[/droid.+; (a(in)?(0(15|59|6[35])|142)p?)/i],[k,[y,"Nothing"],[v,P]],[/; (x67 5g|tikeasy \w+|ac[1789]\d\w+)( b|\))/i,/archos ?(5|gamepad2?|([\w ]*[t1789]|hello) ?\d+[\w ]*)( b|\))/i],[k,[y,"Archos"],[v,x]],[/archos ([\w ]+)( b|\))/i,/; (ac[3-6]\d\w{2,8})( b|\))/i],[k,[y,"Archos"],[v,P]],[/; (n159v)/i],[k,[y,"HMD"],[v,P]],[/(imo) (tab \w+)/i,/(infinix|tecno) (x1101b?|p904|dp(7c|8d|10a)( pro)?|p70[1-3]a?|p904|t1101)/i],[y,k,[v,x]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus(?! zenw)|dell|jolla|meizu|motorola|polytron|tecno|micromax|advan)[-_ ]?([-\w]*)/i,/; (blu|hmd|imo|infinix|lava|oneplus|tcl)[_ ]([\w\+ ]+?)(?: bui|\)|; r)/i,/(hp) ([\w ]+\w)/i,/(microsoft); (lumia[\w ]+)/i,/(oppo) ?([\w ]+) bui/i],[y,k,[v,P]],[/(kobo)\s(ereader|touch)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i],[y,k,[v,x]],[/(surface duo)/i],[k,[y,ae],[v,x]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[k,[y,"Fairphone"],[v,P]],[/((?:tegranote|shield t(?!.+d tv))[\w- ]*?)(?: b|\))/i],[k,[y,oe],[v,x]],[/(sprint) (\w+)/i],[y,k,[v,P]],[/(kin\.[onetw]{3})/i],[[k,/\./g," "],[y,ae],[v,P]],[/droid.+; ([c6]+|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[k,[y,me],[v,x]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[k,[y,me],[v,P]],[/smart-tv.+(samsung)/i],[y,[v,E]],[/hbbtv.+maple;(\d+)/i],[[k,/^/,"SmartTV"],[y,pe],[v,E]],[/(vizio)(?: |.+model\/)(\w+-\w+)/i,/tcast.+(lg)e?. ([-\w]+)/i],[y,k,[v,E]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[y,ie],[v,E]],[/(apple) ?tv/i],[y,[k,J+" TV"],[v,E]],[/crkey.*devicetype\/chromecast/i],[[k,_e+" Third Generation"],[y,ee],[v,E]],[/crkey.*devicetype\/([^/]*)/i],[[k,/^/,"Chromecast "],[y,ee],[v,E]],[/fuchsia.*crkey/i],[[k,_e+" Nest Hub"],[y,ee],[v,E]],[/crkey/i],[[k,_e],[y,ee],[v,E]],[/(portaltv)/i],[k,[y,ye],[v,E]],[/droid.+aft(\w+)( bui|\))/i],[k,[y,X],[v,E]],[/(shield \w+ tv)/i],[k,[y,oe],[v,E]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[k,[y,le],[v,E]],[/(bravia[\w ]+)( bui|\))/i],[k,[y,he],[v,E]],[/(mi(tv|box)-?\w+) bui/i],[k,[y,ue],[v,E]],[/Hbbtv.*(technisat) (.*);/i],[y,k,[v,E]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[y,/.+\/(\w+)/,"$1",Ue,{LG:"lge"}],[k,ze],[v,E]],[/droid.+; ([\w- ]+) (?:android tv|smart[- ]?tv)/i],[k,[v,E]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:|large screen[\w ]+safari)\b/i],[[v,E]],[/(playstation \w+)/i],[k,[y,he],[v,T]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[k,[y,ae],[v,T]],[/(ouya)/i,/(nintendo) (\w+)/i,/(retroid) (pocket ([^\)]+))/i],[y,k,[v,T]],[/droid.+; (shield)( bui|\))/i],[k,[y,oe],[v,T]],[/\b(sm-[lr]\d\d[0156][fnuw]?s?|gear live)\b/i],[k,[y,pe],[v,D]],[/((pebble))app/i,/(asus|google|lg|oppo) ((pixel |zen)?watch[\w ]*)( bui|\))/i],[y,k,[v,D]],[/(ow(?:19|20)?we?[1-3]{1,3})/i],[k,[y,de],[v,D]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[k,[y,J],[v,D]],[/(opwwe\d{3})/i],[k,[y,ce],[v,D]],[/(moto 360)/i],[k,[y,ne],[v,D]],[/(smartwatch 3)/i],[k,[y,he],[v,D]],[/(g watch r)/i],[k,[y,ie],[v,D]],[/droid.+; (wt63?0{2,3})\)/i],[k,[y,me],[v,D]],[/droid.+; (glass) \d/i],[k,[y,ee],[v,L]],[/(pico) (4|neo3(?: link|pro)?)/i],[y,k,[v,L]],[/(quest( \d| pro)?s?).+vr/i],[k,[y,ye],[v,L]],[/mobile vr; rv.+firefox/i],[[v,L]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[y,[v,I]],[/(aeobc)\b/i],[k,[y,X],[v,I]],[/(homepod).+mac os/i],[k,[y,J],[v,I]],[/windows iot/i],[[v,I]],[/droid .+?; ([^;]+?)(?: bui|; wv\)|\) applew).+?(mobile|vr|\d) safari/i],[k,[v,Ue,{mobile:"Mobile",xr:"VR","*":x}]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[v,x]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[v,P]],[/droid .+?; ([\w\. -]+)( bui|\))/i],[k,[y,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[S,[w,be+"HTML"]],[/(arkweb)\/([\w\.]+)/i],[w,S],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[S,[w,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna|servo)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[w,S],[/ladybird\//i],[[w,"LibWeb"]],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[S,w]],os:[[/(windows nt) (6\.[23]); arm/i],[[w,/N/,"R"],[S,Ue,Be]],[/(windows (?:phone|mobile|iot))(?: os)?[\/ ]?([\d\.]*( se)?)/i,/(windows)[\/ ](1[01]|2000|3\.1|7|8(\.1)?|9[58]|me|server 20\d\d( r2)?|vista|xp)/i],[w,S],[/windows nt ?([\d\.\)]*)(?!.+xbox)/i,/\bwin(?=3| ?9|n)(?:nt| 9x )?([\d\.;]*)/i],[[S,/(;|\))/g,"",Ue,Be],[w,ke]],[/(windows ce)\/?([\d\.]*)/i],[w,S],[/[adehimnop]{4,7}\b(?:.*os ([\w]+) like mac|; opera)/i,/(?:ios;fbsv\/|iphone.+ios[\/ ])([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[S,/_/g,"."],[w,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+(haiku|morphos))/i],[[w,"macOS"],[S,/_/g,"."]],[/android ([\d\.]+).*crkey/i],[S,[w,_e+" Android"]],[/fuchsia.*crkey\/([\d\.]+)/i],[S,[w,_e+" Fuchsia"]],[/crkey\/([\d\.]+).*devicetype\/smartspeaker/i],[S,[w,_e+" SmartSpeaker"]],[/linux.*crkey\/([\d\.]+)/i],[S,[w,_e+" Linux"]],[/crkey\/([\d\.]+)/i],[S,[w,_e]],[/droid ([\w\.]+)\b.+(android[- ]x86)/i],[S,w],[/(ubuntu) ([\w\.]+) like android/i],[[w,/(.+)/,"$1 Touch"],S],[/(harmonyos)[\/ ]?([\d\.]*)/i,/(android|bada|blackberry|kaios|maemo|meego|openharmony|qnx|rim tablet os|sailfish|series40|symbian|tizen)\w*[-\/\.; ]?([\d\.]*)/i],[w,S],[/\(bb(10);/i],[S,[w,Z]],[/(?:symbian ?os|symbos|s60(?=;)|series ?60)[-\/ ]?([\w\.]*)/i],[S,[w,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[S,[w,we+" OS"]],[/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i,/webos(?:[ \/]?|\.tv-20(?=2[2-9]))(\d[\d\.]*)/i],[S,[w,"webOS"]],[/web0s;.+?(?:chr[o0]me|safari)\/(\d+)/i],[[S,Ue,{25:"120",24:"108",23:"94",22:"87",6:"79",5:"68",4:"53",3:"38",2:"538",1:"537","*":"TV"}],[w,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[S,[w,"watchOS"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[w,"Chrome OS"],S],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) (\w+)/i,/(xbox); +xbox ([^\);]+)/i,/(pico) .+os([\w\.]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/linux.+(mint)[\/\(\) ]?([\w\.]*)/i,/(mageia|vectorlinux|fuchsia|arcaos|arch(?= ?linux))[;l ]([\d\.]*)/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire|knoppix)(?: gnu[\/ ]linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/\b(aix)[; ]([1-9\.]{0,4})/i,/(hurd|linux|morphos)(?: (?:arm|x86|ppc)\w*| ?)([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) ?(r\d)?/i],[w,S],[/(sunos) ?([\d\.]*)/i],[[w,"Solaris"],S],[/\b(beos|os\/2|amigaos|openvms|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[w,S]]},Ge=(Fe.call((n={init:{},isIgnore:{},isIgnoreRgx:{},toString:{}}).init,[[u,[w,S,R,v]],[m,[C]],[f,[v,k,y]],[g,[w,S]],[_,[w,S]]]),Fe.call(n.isIgnore,[[u,[S,R]],[g,[S]],[_,[S]]]),Fe.call(n.isIgnoreRgx,[[u,/ ?browser$/i],[_,/ ?os$/i]]),Fe.call(n.toString,[[u,[w,S]],[m,[C]],[f,[y,k]],[g,[w,S]],[_,[w,S]]]),n),Qe=function(e,t){var r=Ge.init[t],s=Ge.isIgnore[t]||0,i=Ge.isIgnoreRgx[t]||0,a=Ge.toString[t]||0;function n(){Fe.call(this,r)}return n.prototype.getItem=function(){return e},n.prototype.withClientHints=function(){return xe?xe.getHighEntropyValues(K).then(function(t){return e.setCH(new We(t,!1)).parseCH().get()}):e.parseCH().get()},n.prototype.withFeatureCheck=function(){return e.detectFeature().get()},t!=b&&(n.prototype.is=function(e){var t=!1;for(var r in this)if(this.hasOwnProperty(r)&&!De(s,r)&&Oe(i?Ae(i,this[r]):this[r])==Oe(i?Ae(i,e):e)){if(t=!0,e!=p)break}else if(e==p&&t){t=!t;break}return t},n.prototype.toString=function(){var e=c;for(var t in a)typeof this[a[t]]!==p&&(e+=(e?" ":c)+this[a[t]]);return e||p}),xe||(n.prototype.then=function(e){var t=this,r=function(){for(var e in t)t.hasOwnProperty(e)&&(this[e]=t[e])};r.prototype={is:n.prototype.is,toString:n.prototype.toString};var s=new r;return e(s),s}),new n};function We(e,t){if(e=e||{},Fe.call(this,K),t)Fe.call(this,[[O,Me(e[$])],[F,Me(e[U])],[P,/\?1/.test(e[V])],[k,Ne(e[G])],[A,Ne(e[Q])],[N,Ne(e[W])],[C,Ne(e[B])],[j,Me(e[H])],[z,Ne(e[q])]]);else for(var r in e)this.hasOwnProperty(r)&&typeof e[r]!==p&&(this[r]=e[r])}function Ke(e,t,r,s){return this.get=function(e){return e?this.data.hasOwnProperty(e)?this.data[e]:a:this.data},this.set=function(e,t){return this.data[e]=t,this},this.setCH=function(e){return this.uaCH=e,this},this.detectFeature=function(){if(Pe&&Pe.userAgent==this.ua)switch(this.itemType){case u:Pe.brave&&typeof Pe.brave.isBrave==d&&this.set(w,"Brave");break;case f:!this.get(v)&&xe&&xe[P]&&this.set(v,P),"Macintosh"==this.get(k)&&Pe&&typeof Pe.standalone!==p&&Pe.maxTouchPoints&&Pe.maxTouchPoints>2&&this.set(k,"iPad").set(v,x);break;case _:!this.get(w)&&xe&&xe[A]&&this.set(w,xe[A]);break;case b:var e=this.data,t=function(t){return e[t].getItem().detectFeature().get()};this.set(u,t(u)).set(m,t(m)).set(f,t(f)).set(g,t(g)).set(_,t(_))}return this},this.parseUA=function(){return this.itemType!=b&&$e.call(this.data,this.ua,this.rgxMap),this.itemType==u&&this.set(R,je(this.get(S))),this},this.parseCH=function(){var e=this.uaCH,t=this.rgxMap;switch(this.itemType){case u:case g:var r,s=e[F]||e[O];if(s)for(var i in s){var n=s[i].brand||s[i],o=s[i].version;this.itemType==u&&!/not.a.brand/i.test(n)&&(!r||/Chrom/.test(r)&&n!=ge||r==be&&/WebView2/.test(n))&&(n=Ue(n,He),(r=this.get(w))&&!/Chrom/.test(r)&&/Chrom/.test(n)||this.set(w,n).set(S,o).set(R,je(o)),r=n),this.itemType==g&&n==ge&&this.set(S,o)}break;case m:var c=e[C];c&&(c&&"64"==e[z]&&(c+="64"),$e.call(this.data,c+";",t));break;case f:if(e[P]&&this.set(v,P),e[k]&&(this.set(k,e[k]),!this.get(v)||!this.get(y))){var d={};$e.call(d,"droid 9; "+e[k]+")",t),!this.get(v)&&d.type&&this.set(v,d.type),!this.get(y)&&d.vendor&&this.set(y,d.vendor)}if(e[j]){var p;if("string"!=typeof e[j])for(var l=0;!p&&l<e[j].length;)p=Ue(e[j][l++],qe);else p=Ue(e[j],qe);this.set(v,p)}break;case _:var h=e[A];if(h){var T=e[N];h==ke&&(T=parseInt(je(T),10)>=13?"11":"10"),this.set(w,h).set(S,T)}this.get(w)==ke&&"Xbox"==e[k]&&this.set(w,"Xbox").set(S,a);break;case b:var x=this.data,E=function(t){return x[t].getItem().setCH(e).parseCH().get()};this.set(u,E(u)).set(m,E(m)).set(f,E(f)).set(g,E(g)).set(_,E(_))}return this},Fe.call(this,[["itemType",e],["ua",t],["uaCH",s],["rgxMap",r],["data",Qe(this,e)]]),this}function Xe(e,t,r){if(typeof e===l?(Le(e,!0)?(typeof t===l&&(r=t),t=e):(r=e,t=a),e=a):typeof e!==h||Le(t,!0)||(r=t,t=a),r&&typeof r.append===d){var s={};r.forEach(function(e,t){s[t]=e}),r=s}if(!(this instanceof Xe))return new Xe(e,t,r).getResult();var i=typeof e===h?e:r&&r[o]?r[o]:Pe&&Pe.userAgent?Pe.userAgent:c,n=new We(r,!0),p=t?function(e,t){var r={},s=t;if(!Le(t))for(var i in s={},t)for(var a in t[i])s[a]=t[i][a].concat(s[a]?s[a]:[]);for(var n in e)r[n]=s[n]&&s[n].length%2==0?s[n].concat(e[n]):e[n];return r}(Ve,t):Ve,w=function(e){return e==b?function(){return new Ke(e,i,p,n).set("ua",i).set(u,this.getBrowser()).set(m,this.getCPU()).set(f,this.getDevice()).set(g,this.getEngine()).set(_,this.getOS()).get()}:function(){return new Ke(e,i,p[e],n).parseUA().get()}};return Fe.call(this,[["getBrowser",w(u)],["getCPU",w(m)],["getDevice",w(f)],["getEngine",w(g)],["getOS",w(_)],["getResult",w(b)],["getUA",function(){return i}],["setUA",function(e){return Ie(e)&&(i=e.length>500?ze(e,500):e),this}]]).setUA(i),this}Xe.VERSION="2.0.4",Xe.BROWSER=Ee([w,S,R,v]),Xe.CPU=Ee([C]),Xe.DEVICE=Ee([k,y,v,T,P,E,x,D,I]),Xe.ENGINE=Xe.OS=Ee([w,S]),typeof t!==p?(e.exports&&(t=e.exports=Xe),t.UAParser=Xe):r.amdO?(s=function(){return Xe}.call(t,r,t,e))===a||(e.exports=s):Te&&(i.UAParser=Xe);var Je=Te&&(i.jQuery||i.Zepto);if(Je&&!Je.ua){var Ye=new Xe;Je.ua=Ye.getResult(),Je.ua.get=function(){return Ye.getUA()},Je.ua.set=function(e){Ye.setUA(e);var t=Ye.getResult();for(var r in t)Je.ua[r]=t[r]}}}("object"==typeof window?window:this)},2183:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Chrome111=void 0;const s=r(7363),i=r(3953),a=r(2994),n=r(8046),o=r(4893),c=r(3303),d=r(5544),p=r(5938),l=r(4256),h=r(1305),u=new a.Logger("Chrome111"),m="Chrome111",f={OS:1024,MIS:1024};class g extends i.EnhancedEventEmitter{_closed=!1;_direction;_remoteSdp;_getSendExtendedRtpCapabilities;_forcedLocalDtlsRole;_pc;_mapMidTransceiver=new Map;_sendStream=new MediaStream;_hasDataChannelMediaSection=!1;_nextSendSctpStreamId=0;_transportReady=!1;static createFactory(){return{name:m,factory:e=>new g(e),getNativeRtpCapabilities:async()=>{u.debug("getNativeRtpCapabilities()");let e=new RTCPeerConnection({iceServers:[],iceTransportPolicy:"all",bundlePolicy:"max-bundle",rtcpMuxPolicy:"require"});try{e.addTransceiver("audio"),e.addTransceiver("video",{sendEncodings:[{scalabilityMode:"L3T3"}]});const t=await e.createOffer();try{e.close()}catch(e){}e=void 0;const r=s.parse(t.sdp);return g.getLocalRtpCapabilities(r)}catch(t){try{e?.close()}catch(e){}throw e=void 0,t}},getNativeSctpCapabilities:async()=>(u.debug("getNativeSctpCapabilities()"),{numStreams:f})}}static getLocalRtpCapabilities(e){const t=d.extractRtpCapabilities({sdpObject:e});return l.addNackSupportForOpus(t),t}constructor({direction:e,iceParameters:t,iceCandidates:r,dtlsParameters:s,sctpParameters:i,iceServers:a,iceTransportPolicy:n,additionalSettings:o,getSendExtendedRtpCapabilities:c}){super(),u.debug("constructor()"),this._direction=e,this._remoteSdp=new h.RemoteSdp({iceParameters:t,iceCandidates:r,dtlsParameters:s,sctpParameters:i}),this._getSendExtendedRtpCapabilities=c,s.role&&"auto"!==s.role&&(this._forcedLocalDtlsRole="server"===s.role?"client":"server"),this._pc=new RTCPeerConnection({iceServers:a??[],iceTransportPolicy:n??"all",bundlePolicy:"max-bundle",rtcpMuxPolicy:"require",...o}),this._pc.addEventListener("icegatheringstatechange",this.onIceGatheringStateChange),this._pc.addEventListener("icecandidateerror",this.onIceCandidateError),this._pc.connectionState?this._pc.addEventListener("connectionstatechange",this.onConnectionStateChange):(u.warn("run() | pc.connectionState not supported, using pc.iceConnectionState"),this._pc.addEventListener("iceconnectionstatechange",this.onIceConnectionStateChange))}get name(){return m}close(){if(u.debug("close()"),!this._closed){this._closed=!0;try{this._pc.close()}catch(e){}this._pc.removeEventListener("icegatheringstatechange",this.onIceGatheringStateChange),this._pc.removeEventListener("icecandidateerror",this.onIceCandidateError),this._pc.removeEventListener("connectionstatechange",this.onConnectionStateChange),this._pc.removeEventListener("iceconnectionstatechange",this.onIceConnectionStateChange),this.emit("@close"),super.close()}}async updateIceServers(e){this.assertNotClosed(),u.debug("updateIceServers()");const t=this._pc.getConfiguration();t.iceServers=e,this._pc.setConfiguration(t)}async restartIce(e){if(this.assertNotClosed(),u.debug("restartIce()"),this._remoteSdp.updateIceParameters(e),this._transportReady)if("send"===this._direction){const e=await this._pc.createOffer({iceRestart:!0});u.debug("restartIce() | calling pc.setLocalDescription() [offer:%o]",e),await this._pc.setLocalDescription(e);const t={type:"answer",sdp:this._remoteSdp.getSdp()};u.debug("restartIce() | calling pc.setRemoteDescription() [answer:%o]",t),await this._pc.setRemoteDescription(t)}else{const e={type:"offer",sdp:this._remoteSdp.getSdp()};u.debug("restartIce() | calling pc.setRemoteDescription() [offer:%o]",e),await this._pc.setRemoteDescription(e);const t=await this._pc.createAnswer();u.debug("restartIce() | calling pc.setLocalDescription() [answer:%o]",t),await this._pc.setLocalDescription(t)}}async getTransportStats(){return this.assertNotClosed(),this._pc.getStats()}async send({track:e,encodings:t,codecOptions:r,codec:i,onRtpSender:a}){if(this.assertNotClosed(),this.assertSendDirection(),u.debug("send() [kind:%s, track.id:%s]",e.kind,e.id),t&&t.length>1){let e=1;for(const r of t){const t=r.scalabilityMode?(0,c.parse)(r.scalabilityMode).temporalLayers:3;t>e&&(e=t)}t.forEach((t,r)=>{t.rid=`r${r}`,t.scalabilityMode=`L1T${e}`})}const o=this._remoteSdp.getNextMediaSectionIdx(),l=this._pc.addTransceiver(e,{direction:"sendonly",streams:[this._sendStream],sendEncodings:t});a&&a(l.sender);const h=await this._pc.createOffer();let m=s.parse(h.sdp);m.extmapAllowMixed&&this._remoteSdp.setSessionExtmapAllowMixed();const f=g.getLocalRtpCapabilities(m),_=this._getSendExtendedRtpCapabilities(f),b=n.getSendingRtpParameters(e.kind,_);b.codecs=n.reduceCodecs(b.codecs,i);const w=n.getSendingRemoteRtpParameters(e.kind,_);w.codecs=n.reduceCodecs(w.codecs,i),this._transportReady||await this.setupTransport({localDtlsRole:this._forcedLocalDtlsRole??"client",localSdpObject:m}),u.debug("send() | calling pc.setLocalDescription() [offer:%o]",h),await this._pc.setLocalDescription(h);const v=l.mid;b.mid=v,m=s.parse(this._pc.localDescription.sdp);const y=m.media[o.idx];if(b.rtcp.cname=d.getCname({offerMediaObject:y}),t)if(1===t.length){const e=p.getRtpEncodings({offerMediaObject:y});Object.assign(e[0],t[0]),b.encodings=e}else b.encodings=t;else b.encodings=p.getRtpEncodings({offerMediaObject:y});this._remoteSdp.send({offerMediaObject:y,reuseMid:o.reuseMid,offerRtpParameters:b,answerRtpParameters:w,codecOptions:r});const S={type:"answer",sdp:this._remoteSdp.getSdp()};return u.debug("send() | calling pc.setRemoteDescription() [answer:%o]",S),await this._pc.setRemoteDescription(S),this._mapMidTransceiver.set(v,l),{localId:v,rtpParameters:b,rtpSender:l.sender}}async stopSending(e){if(this.assertSendDirection(),u.debug("stopSending() [localId:%s]",e),this._closed)return;const t=this._mapMidTransceiver.get(e);if(!t)throw new Error("associated RTCRtpTransceiver not found");if(t.sender.replaceTrack(null),this._pc.removeTrack(t.sender),this._remoteSdp.closeMediaSection(t.mid))try{t.stop()}catch(e){}const r=await this._pc.createOffer();u.debug("stopSending() | calling pc.setLocalDescription() [offer:%o]",r),await this._pc.setLocalDescription(r);const s={type:"answer",sdp:this._remoteSdp.getSdp()};u.debug("stopSending() | calling pc.setRemoteDescription() [answer:%o]",s),await this._pc.setRemoteDescription(s),this._mapMidTransceiver.delete(e)}async pauseSending(e){this.assertNotClosed(),this.assertSendDirection(),u.debug("pauseSending() [localId:%s]",e);const t=this._mapMidTransceiver.get(e);if(!t)throw new Error("associated RTCRtpTransceiver not found");t.direction="inactive",this._remoteSdp.pauseMediaSection(e);const r=await this._pc.createOffer();u.debug("pauseSending() | calling pc.setLocalDescription() [offer:%o]",r),await this._pc.setLocalDescription(r);const s={type:"answer",sdp:this._remoteSdp.getSdp()};u.debug("pauseSending() | calling pc.setRemoteDescription() [answer:%o]",s),await this._pc.setRemoteDescription(s)}async resumeSending(e){this.assertNotClosed(),this.assertSendDirection(),u.debug("resumeSending() [localId:%s]",e);const t=this._mapMidTransceiver.get(e);if(this._remoteSdp.resumeSendingMediaSection(e),!t)throw new Error("associated RTCRtpTransceiver not found");t.direction="sendonly";const r=await this._pc.createOffer();u.debug("resumeSending() | calling pc.setLocalDescription() [offer:%o]",r),await this._pc.setLocalDescription(r);const s={type:"answer",sdp:this._remoteSdp.getSdp()};u.debug("resumeSending() | calling pc.setRemoteDescription() [answer:%o]",s),await this._pc.setRemoteDescription(s)}async replaceTrack(e,t){this.assertNotClosed(),this.assertSendDirection(),t?u.debug("replaceTrack() [localId:%s, track.id:%s]",e,t.id):u.debug("replaceTrack() [localId:%s, no track]",e);const r=this._mapMidTransceiver.get(e);if(!r)throw new Error("associated RTCRtpTransceiver not found");await r.sender.replaceTrack(t)}async setMaxSpatialLayer(e,t){this.assertNotClosed(),this.assertSendDirection(),u.debug("setMaxSpatialLayer() [localId:%s, spatialLayer:%s]",e,t);const r=this._mapMidTransceiver.get(e);if(!r)throw new Error("associated RTCRtpTransceiver not found");const s=r.sender.getParameters();s.encodings.forEach((e,r)=>{e.active=r<=t}),await r.sender.setParameters(s),this._remoteSdp.muxMediaSectionSimulcast(e,s.encodings);const i=await this._pc.createOffer();u.debug("setMaxSpatialLayer() | calling pc.setLocalDescription() [offer:%o]",i),await this._pc.setLocalDescription(i);const a={type:"answer",sdp:this._remoteSdp.getSdp()};u.debug("setMaxSpatialLayer() | calling pc.setRemoteDescription() [answer:%o]",a),await this._pc.setRemoteDescription(a)}async setRtpEncodingParameters(e,t){this.assertNotClosed(),this.assertSendDirection(),u.debug("setRtpEncodingParameters() [localId:%s, params:%o]",e,t);const r=this._mapMidTransceiver.get(e);if(!r)throw new Error("associated RTCRtpTransceiver not found");const s=r.sender.getParameters();s.encodings.forEach((e,r)=>{s.encodings[r]={...e,...t}}),await r.sender.setParameters(s),this._remoteSdp.muxMediaSectionSimulcast(e,s.encodings);const i=await this._pc.createOffer();u.debug("setRtpEncodingParameters() | calling pc.setLocalDescription() [offer:%o]",i),await this._pc.setLocalDescription(i);const a={type:"answer",sdp:this._remoteSdp.getSdp()};u.debug("setRtpEncodingParameters() | calling pc.setRemoteDescription() [answer:%o]",a),await this._pc.setRemoteDescription(a)}async getSenderStats(e){this.assertNotClosed(),this.assertSendDirection();const t=this._mapMidTransceiver.get(e);if(!t)throw new Error("associated RTCRtpTransceiver not found");return t.sender.getStats()}async sendDataChannel({ordered:e,maxPacketLifeTime:t,maxRetransmits:r,label:i,protocol:a}){this.assertNotClosed(),this.assertSendDirection();const n={negotiated:!0,id:this._nextSendSctpStreamId,ordered:e,maxPacketLifeTime:t,maxRetransmits:r,protocol:a};u.debug("sendDataChannel() [options:%o]",n);const o=this._pc.createDataChannel(i,n);if(this._nextSendSctpStreamId=++this._nextSendSctpStreamId%f.MIS,!this._hasDataChannelMediaSection){const e=await this._pc.createOffer(),t=s.parse(e.sdp),r=t.media.find(e=>"application"===e.type);this._transportReady||await this.setupTransport({localDtlsRole:this._forcedLocalDtlsRole??"client",localSdpObject:t}),u.debug("sendDataChannel() | calling pc.setLocalDescription() [offer:%o]",e),await this._pc.setLocalDescription(e),this._remoteSdp.sendSctpAssociation({offerMediaObject:r});const i={type:"answer",sdp:this._remoteSdp.getSdp()};u.debug("sendDataChannel() | calling pc.setRemoteDescription() [answer:%o]",i),await this._pc.setRemoteDescription(i),this._hasDataChannelMediaSection=!0}return{dataChannel:o,sctpStreamParameters:{streamId:n.id,ordered:n.ordered,maxPacketLifeTime:n.maxPacketLifeTime,maxRetransmits:n.maxRetransmits}}}async receive(e){this.assertNotClosed(),this.assertRecvDirection();const t=[],r=new Map;for(const t of e){const{trackId:e,kind:s,rtpParameters:i,streamId:a}=t;u.debug("receive() [trackId:%s, kind:%s]",e,s);const n=i.mid??String(this._mapMidTransceiver.size);r.set(e,n),this._remoteSdp.receive({mid:n,kind:s,offerRtpParameters:i,streamId:a??i.rtcp.cname,trackId:e})}const i={type:"offer",sdp:this._remoteSdp.getSdp()};u.debug("receive() | calling pc.setRemoteDescription() [offer:%o]",i),await this._pc.setRemoteDescription(i);for(const t of e){const{trackId:e,onRtpReceiver:s}=t;if(s){const t=r.get(e),i=this._pc.getTransceivers().find(e=>e.mid===t);if(!i)throw new Error("transceiver not found");s(i.receiver)}}let a=await this._pc.createAnswer();const n=s.parse(a.sdp);for(const t of e){const{trackId:e,rtpParameters:s}=t,i=r.get(e),a=n.media.find(e=>String(e.mid)===i);d.applyCodecParameters({offerRtpParameters:s,answerMediaObject:a})}a={type:"answer",sdp:s.write(n)},this._transportReady||await this.setupTransport({localDtlsRole:this._forcedLocalDtlsRole??"client",localSdpObject:n}),u.debug("receive() | calling pc.setLocalDescription() [answer:%o]",a),await this._pc.setLocalDescription(a);for(const s of e){const{trackId:e}=s,i=r.get(e),a=this._pc.getTransceivers().find(e=>e.mid===i);if(!a)throw new Error("new RTCRtpTransceiver not found");this._mapMidTransceiver.set(i,a),t.push({localId:i,track:a.receiver.track,rtpReceiver:a.receiver})}return t}async stopReceiving(e){if(this.assertRecvDirection(),this._closed)return;for(const t of e){u.debug("stopReceiving() [localId:%s]",t);const e=this._mapMidTransceiver.get(t);if(!e)throw new Error("associated RTCRtpTransceiver not found");this._remoteSdp.closeMediaSection(e.mid)}const t={type:"offer",sdp:this._remoteSdp.getSdp()};u.debug("stopReceiving() | calling pc.setRemoteDescription() [offer:%o]",t),await this._pc.setRemoteDescription(t);const r=await this._pc.createAnswer();u.debug("stopReceiving() | calling pc.setLocalDescription() [answer:%o]",r),await this._pc.setLocalDescription(r);for(const t of e)this._mapMidTransceiver.delete(t)}async pauseReceiving(e){this.assertNotClosed(),this.assertRecvDirection();for(const t of e){u.debug("pauseReceiving() [localId:%s]",t);const e=this._mapMidTransceiver.get(t);if(!e)throw new Error("associated RTCRtpTransceiver not found");e.direction="inactive",this._remoteSdp.pauseMediaSection(t)}const t={type:"offer",sdp:this._remoteSdp.getSdp()};u.debug("pauseReceiving() | calling pc.setRemoteDescription() [offer:%o]",t),await this._pc.setRemoteDescription(t);const r=await this._pc.createAnswer();u.debug("pauseReceiving() | calling pc.setLocalDescription() [answer:%o]",r),await this._pc.setLocalDescription(r)}async resumeReceiving(e){this.assertNotClosed(),this.assertRecvDirection();for(const t of e){u.debug("resumeReceiving() [localId:%s]",t);const e=this._mapMidTransceiver.get(t);if(!e)throw new Error("associated RTCRtpTransceiver not found");e.direction="recvonly",this._remoteSdp.resumeReceivingMediaSection(t)}const t={type:"offer",sdp:this._remoteSdp.getSdp()};u.debug("resumeReceiving() | calling pc.setRemoteDescription() [offer:%o]",t),await this._pc.setRemoteDescription(t);const r=await this._pc.createAnswer();u.debug("resumeReceiving() | calling pc.setLocalDescription() [answer:%o]",r),await this._pc.setLocalDescription(r)}async getReceiverStats(e){this.assertNotClosed(),this.assertRecvDirection();const t=this._mapMidTransceiver.get(e);if(!t)throw new Error("associated RTCRtpTransceiver not found");return t.receiver.getStats()}async receiveDataChannel({sctpStreamParameters:e,label:t,protocol:r}){this.assertNotClosed(),this.assertRecvDirection();const{streamId:i,ordered:a,maxPacketLifeTime:n,maxRetransmits:o}=e,c={negotiated:!0,id:i,ordered:a,maxPacketLifeTime:n,maxRetransmits:o,protocol:r};u.debug("receiveDataChannel() [options:%o]",c);const d=this._pc.createDataChannel(t,c);if(!this._hasDataChannelMediaSection){this._remoteSdp.receiveSctpAssociation();const e={type:"offer",sdp:this._remoteSdp.getSdp()};u.debug("receiveDataChannel() | calling pc.setRemoteDescription() [offer:%o]",e),await this._pc.setRemoteDescription(e);const t=await this._pc.createAnswer();if(!this._transportReady){const e=s.parse(t.sdp);await this.setupTransport({localDtlsRole:this._forcedLocalDtlsRole??"client",localSdpObject:e})}u.debug("receiveDataChannel() | calling pc.setRemoteDescription() [answer:%o]",t),await this._pc.setLocalDescription(t),this._hasDataChannelMediaSection=!0}return{dataChannel:d}}async setupTransport({localDtlsRole:e,localSdpObject:t}){t||(t=s.parse(this._pc.localDescription.sdp));const r=d.extractDtlsParameters({sdpObject:t});r.role=e,this._remoteSdp.updateDtlsRole("client"===e?"server":"client"),await new Promise((e,t)=>{this.safeEmit("@connect",{dtlsParameters:r},e,t)}),this._transportReady=!0}onIceGatheringStateChange=()=>{this.emit("@icegatheringstatechange",this._pc.iceGatheringState)};onIceCandidateError=e=>{this.emit("@icecandidateerror",e)};onConnectionStateChange=()=>{this.emit("@connectionstatechange",this._pc.connectionState)};onIceConnectionStateChange=()=>{switch(this._pc.iceConnectionState){case"checking":this.emit("@connectionstatechange","connecting");break;case"connected":case"completed":this.emit("@connectionstatechange","connected");break;case"failed":this.emit("@connectionstatechange","failed");break;case"disconnected":this.emit("@connectionstatechange","disconnected");break;case"closed":this.emit("@connectionstatechange","closed")}};assertNotClosed(){if(this._closed)throw new o.InvalidStateError("method called in a closed handler")}assertSendDirection(){if("send"!==this._direction)throw new Error('method can just be called for handlers with "send" direction')}assertRecvDirection(){if("recv"!==this._direction)throw new Error('method can just be called for handlers with "recv" direction')}}t.Chrome111=g},2196:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default="ffffffff-ffff-ffff-ffff-ffffffffffff"},2291:(e,t)=>{"use strict";let r;Object.defineProperty(t,"__esModule",{value:!0});const s=new Uint8Array(16);t.default=function(){if(!r){if("undefined"==typeof crypto||!crypto.getRandomValues)throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");r=crypto.getRandomValues.bind(crypto)}return r(s)}},2292:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Firefox120=void 0;const s=r(7363),i=r(3953),a=r(2994),n=r(4893),o=r(8046),c=r(3303),d=r(5544),p=r(5938),l=r(1305),h=new a.Logger("Firefox120"),u="Firefox120",m={OS:16,MIS:2048};class f extends i.EnhancedEventEmitter{_closed=!1;_direction;_remoteSdp;_getSendExtendedRtpCapabilities;_pc;_mapMidTransceiver=new Map;_sendStream=new MediaStream;_hasDataChannelMediaSection=!1;_nextSendSctpStreamId=0;_transportReady=!1;static createFactory(){return{name:u,factory:e=>new f(e),getNativeRtpCapabilities:async()=>{h.debug("getNativeRtpCapabilities()");let e=new RTCPeerConnection({iceServers:[],iceTransportPolicy:"all",bundlePolicy:"max-bundle",rtcpMuxPolicy:"require"});const t=document.createElement("canvas");t.getContext("2d");const r=t.captureStream().getVideoTracks()[0];try{e.addTransceiver("audio",{direction:"sendrecv"}),e.addTransceiver(r,{direction:"sendrecv",sendEncodings:[{rid:"r0",maxBitrate:1e5},{rid:"r1",maxBitrate:5e5}]});const i=await e.createOffer();try{t.remove()}catch(e){}try{r.stop()}catch(e){}try{e.close()}catch(e){}e=void 0;const a=s.parse(i.sdp);return f.getLocalRtpCapabilities(a)}catch(s){try{t.remove()}catch(e){}try{r.stop()}catch(e){}try{e?.close()}catch(e){}throw e=void 0,s}},getNativeSctpCapabilities:async()=>(h.debug("getNativeSctpCapabilities()"),{numStreams:m})}}static getLocalRtpCapabilities(e){return d.extractRtpCapabilities({sdpObject:e})}constructor({direction:e,iceParameters:t,iceCandidates:r,dtlsParameters:s,sctpParameters:i,iceServers:a,iceTransportPolicy:n,additionalSettings:o,getSendExtendedRtpCapabilities:c}){super(),h.debug("constructor()"),this._direction=e,this._remoteSdp=new l.RemoteSdp({iceParameters:t,iceCandidates:r,dtlsParameters:s,sctpParameters:i}),this._getSendExtendedRtpCapabilities=c,this._pc=new RTCPeerConnection({iceServers:a??[],iceTransportPolicy:n??"all",bundlePolicy:"max-bundle",rtcpMuxPolicy:"require",...o}),this._pc.addEventListener("icegatheringstatechange",this.onIceGatheringStateChange),this._pc.addEventListener("icecandidateerror",this.onIceCandidateError),this._pc.connectionState?this._pc.addEventListener("connectionstatechange",this.onConnectionStateChange):(h.warn("run() | pc.connectionState not supported, using pc.iceConnectionState"),this._pc.addEventListener("iceconnectionstatechange",this.onIceConnectionStateChange))}get name(){return u}close(){if(h.debug("close()"),!this._closed){this._closed=!0;try{this._pc.close()}catch(e){}this._pc.removeEventListener("icegatheringstatechange",this.onIceGatheringStateChange),this._pc.removeEventListener("icecandidateerror",this.onIceCandidateError),this._pc.removeEventListener("connectionstatechange",this.onConnectionStateChange),this._pc.removeEventListener("iceconnectionstatechange",this.onIceConnectionStateChange),this.emit("@close"),super.close()}}async updateIceServers(e){throw this.assertNotClosed(),new n.UnsupportedError("not supported")}async restartIce(e){if(this.assertNotClosed(),h.debug("restartIce()"),this._remoteSdp.updateIceParameters(e),this._transportReady)if("send"===this._direction){const e=await this._pc.createOffer({iceRestart:!0});h.debug("restartIce() | calling pc.setLocalDescription() [offer:%o]",e),await this._pc.setLocalDescription(e);const t={type:"answer",sdp:this._remoteSdp.getSdp()};h.debug("restartIce() | calling pc.setRemoteDescription() [answer:%o]",t),await this._pc.setRemoteDescription(t)}else{const e={type:"offer",sdp:this._remoteSdp.getSdp()};h.debug("restartIce() | calling pc.setRemoteDescription() [offer:%o]",e),await this._pc.setRemoteDescription(e);const t=await this._pc.createAnswer();h.debug("restartIce() | calling pc.setLocalDescription() [answer:%o]",t),await this._pc.setLocalDescription(t)}}async getTransportStats(){return this.assertNotClosed(),this._pc.getStats()}async send({track:e,encodings:t,codecOptions:r,codec:i,onRtpSender:a}){this.assertNotClosed(),this.assertSendDirection(),h.debug("send() [kind:%s, track.id:%s]",e.kind,e.id),t&&t.length>1&&t.forEach((e,t)=>{e.rid=`r${t}`});const n=this._pc.addTransceiver(e,{direction:"sendonly",streams:[this._sendStream],sendEncodings:t});a&&a(n.sender);const l=await this._pc.createOffer();let u=s.parse(l.sdp);u.extmapAllowMixed&&this._remoteSdp.setSessionExtmapAllowMixed();const m=f.getLocalRtpCapabilities(u),g=this._getSendExtendedRtpCapabilities(m),_=o.getSendingRtpParameters(e.kind,g);_.codecs=o.reduceCodecs(_.codecs,i);const b=o.getSendingRemoteRtpParameters(e.kind,g);b.codecs=o.reduceCodecs(b.codecs,i),this._transportReady||await this.setupTransport({localDtlsRole:"client",localSdpObject:u});const w=(0,c.parse)((t??[{}])[0].scalabilityMode);h.debug("send() | calling pc.setLocalDescription() [offer:%o]",l),await this._pc.setLocalDescription(l);const v=n.mid;_.mid=v,u=s.parse(this._pc.localDescription.sdp);const y=u.media[u.media.length-1];if(_.rtcp.cname=d.getCname({offerMediaObject:y}),t)if(1===t.length){const e=p.getRtpEncodings({offerMediaObject:y});Object.assign(e[0],t[0]),_.encodings=e}else _.encodings=t;else _.encodings=p.getRtpEncodings({offerMediaObject:y});if(_.encodings.length>1&&("video/vp8"===_.codecs[0].mimeType.toLowerCase()||"video/h264"===_.codecs[0].mimeType.toLowerCase()))for(const e of _.encodings)e.scalabilityMode?e.scalabilityMode=`L1T${w.temporalLayers}`:e.scalabilityMode="L1T3";this._remoteSdp.send({offerMediaObject:y,offerRtpParameters:_,answerRtpParameters:b,codecOptions:r});const S={type:"answer",sdp:this._remoteSdp.getSdp()};return h.debug("send() | calling pc.setRemoteDescription() [answer:%o]",S),await this._pc.setRemoteDescription(S),this._mapMidTransceiver.set(v,n),{localId:v,rtpParameters:_,rtpSender:n.sender}}async stopSending(e){if(this.assertSendDirection(),h.debug("stopSending() [localId:%s]",e),this._closed)return;const t=this._mapMidTransceiver.get(e);if(!t)throw new Error("associated transceiver not found");t.sender.replaceTrack(null),this._pc.removeTrack(t.sender),this._remoteSdp.disableMediaSection(t.mid);const r=await this._pc.createOffer();h.debug("stopSending() | calling pc.setLocalDescription() [offer:%o]",r),await this._pc.setLocalDescription(r);const s={type:"answer",sdp:this._remoteSdp.getSdp()};h.debug("stopSending() | calling pc.setRemoteDescription() [answer:%o]",s),await this._pc.setRemoteDescription(s),this._mapMidTransceiver.delete(e)}async pauseSending(e){this.assertNotClosed(),this.assertSendDirection(),h.debug("pauseSending() [localId:%s]",e);const t=this._mapMidTransceiver.get(e);if(!t)throw new Error("associated RTCRtpTransceiver not found");t.direction="inactive",this._remoteSdp.pauseMediaSection(e);const r=await this._pc.createOffer();h.debug("pauseSending() | calling pc.setLocalDescription() [offer:%o]",r),await this._pc.setLocalDescription(r);const s={type:"answer",sdp:this._remoteSdp.getSdp()};h.debug("pauseSending() | calling pc.setRemoteDescription() [answer:%o]",s),await this._pc.setRemoteDescription(s)}async resumeSending(e){this.assertNotClosed(),this.assertSendDirection(),h.debug("resumeSending() [localId:%s]",e);const t=this._mapMidTransceiver.get(e);if(!t)throw new Error("associated RTCRtpTransceiver not found");t.direction="sendonly",this._remoteSdp.resumeSendingMediaSection(e);const r=await this._pc.createOffer();h.debug("resumeSending() | calling pc.setLocalDescription() [offer:%o]",r),await this._pc.setLocalDescription(r);const s={type:"answer",sdp:this._remoteSdp.getSdp()};h.debug("resumeSending() | calling pc.setRemoteDescription() [answer:%o]",s),await this._pc.setRemoteDescription(s)}async replaceTrack(e,t){this.assertNotClosed(),this.assertSendDirection(),t?h.debug("replaceTrack() [localId:%s, track.id:%s]",e,t.id):h.debug("replaceTrack() [localId:%s, no track]",e);const r=this._mapMidTransceiver.get(e);if(!r)throw new Error("associated RTCRtpTransceiver not found");await r.sender.replaceTrack(t)}async setMaxSpatialLayer(e,t){this.assertNotClosed(),this.assertSendDirection(),h.debug("setMaxSpatialLayer() [localId:%s, spatialLayer:%s]",e,t);const r=this._mapMidTransceiver.get(e);if(!r)throw new Error("associated transceiver not found");const s=r.sender.getParameters();s.encodings.forEach((e,r)=>{e.active=r<=t}),await r.sender.setParameters(s),this._remoteSdp.muxMediaSectionSimulcast(e,s.encodings);const i=await this._pc.createOffer();h.debug("setMaxSpatialLayer() | calling pc.setLocalDescription() [offer:%o]",i),await this._pc.setLocalDescription(i);const a={type:"answer",sdp:this._remoteSdp.getSdp()};h.debug("setMaxSpatialLayer() | calling pc.setRemoteDescription() [answer:%o]",a),await this._pc.setRemoteDescription(a)}async setRtpEncodingParameters(e,t){this.assertNotClosed(),this.assertSendDirection(),h.debug("setRtpEncodingParameters() [localId:%s, params:%o]",e,t);const r=this._mapMidTransceiver.get(e);if(!r)throw new Error("associated RTCRtpTransceiver not found");const s=r.sender.getParameters();s.encodings.forEach((e,r)=>{s.encodings[r]={...e,...t}}),await r.sender.setParameters(s),this._remoteSdp.muxMediaSectionSimulcast(e,s.encodings);const i=await this._pc.createOffer();h.debug("setRtpEncodingParameters() | calling pc.setLocalDescription() [offer:%o]",i),await this._pc.setLocalDescription(i);const a={type:"answer",sdp:this._remoteSdp.getSdp()};h.debug("setRtpEncodingParameters() | calling pc.setRemoteDescription() [answer:%o]",a),await this._pc.setRemoteDescription(a)}async getSenderStats(e){this.assertNotClosed(),this.assertSendDirection();const t=this._mapMidTransceiver.get(e);if(!t)throw new Error("associated RTCRtpTransceiver not found");return t.sender.getStats()}async sendDataChannel({ordered:e,maxPacketLifeTime:t,maxRetransmits:r,label:i,protocol:a}){this.assertNotClosed(),this.assertSendDirection();const n={negotiated:!0,id:this._nextSendSctpStreamId,ordered:e,maxPacketLifeTime:t,maxRetransmits:r,protocol:a};h.debug("sendDataChannel() [options:%o]",n);const o=this._pc.createDataChannel(i,n);if(this._nextSendSctpStreamId=++this._nextSendSctpStreamId%m.MIS,!this._hasDataChannelMediaSection){const e=await this._pc.createOffer(),t=s.parse(e.sdp),r=t.media.find(e=>"application"===e.type);this._transportReady||await this.setupTransport({localDtlsRole:"client",localSdpObject:t}),h.debug("sendDataChannel() | calling pc.setLocalDescription() [offer:%o]",e),await this._pc.setLocalDescription(e),this._remoteSdp.sendSctpAssociation({offerMediaObject:r});const i={type:"answer",sdp:this._remoteSdp.getSdp()};h.debug("sendDataChannel() | calling pc.setRemoteDescription() [answer:%o]",i),await this._pc.setRemoteDescription(i),this._hasDataChannelMediaSection=!0}return{dataChannel:o,sctpStreamParameters:{streamId:n.id,ordered:n.ordered,maxPacketLifeTime:n.maxPacketLifeTime,maxRetransmits:n.maxRetransmits}}}async receive(e){this.assertNotClosed(),this.assertRecvDirection();const t=[],r=new Map;for(const t of e){const{trackId:e,kind:s,rtpParameters:i,streamId:a}=t;h.debug("receive() [trackId:%s, kind:%s]",e,s);const n=i.mid??String(this._mapMidTransceiver.size);r.set(e,n),this._remoteSdp.receive({mid:n,kind:s,offerRtpParameters:i,streamId:a??i.rtcp.cname,trackId:e})}const i={type:"offer",sdp:this._remoteSdp.getSdp()};h.debug("receive() | calling pc.setRemoteDescription() [offer:%o]",i),await this._pc.setRemoteDescription(i);for(const t of e){const{trackId:e,onRtpReceiver:s}=t;if(s){const t=r.get(e),i=this._pc.getTransceivers().find(e=>e.mid===t);if(!i)throw new Error("transceiver not found");s(i.receiver)}}let a=await this._pc.createAnswer();const n=s.parse(a.sdp);for(const t of e){const{trackId:e,rtpParameters:i}=t,o=r.get(e),c=n.media.find(e=>String(e.mid)===o);d.applyCodecParameters({offerRtpParameters:i,answerMediaObject:c}),a={type:"answer",sdp:s.write(n)}}this._transportReady||await this.setupTransport({localDtlsRole:"client",localSdpObject:n}),h.debug("receive() | calling pc.setLocalDescription() [answer:%o]",a),await this._pc.setLocalDescription(a);for(const s of e){const{trackId:e}=s,i=r.get(e),a=this._pc.getTransceivers().find(e=>e.mid===i);if(!a)throw new Error("new RTCRtpTransceiver not found");this._mapMidTransceiver.set(i,a),t.push({localId:i,track:a.receiver.track,rtpReceiver:a.receiver})}return t}async stopReceiving(e){if(this.assertRecvDirection(),this._closed)return;for(const t of e){h.debug("stopReceiving() [localId:%s]",t);const e=this._mapMidTransceiver.get(t);if(!e)throw new Error("associated RTCRtpTransceiver not found");this._remoteSdp.closeMediaSection(e.mid)}const t={type:"offer",sdp:this._remoteSdp.getSdp()};h.debug("stopReceiving() | calling pc.setRemoteDescription() [offer:%o]",t),await this._pc.setRemoteDescription(t);const r=await this._pc.createAnswer();h.debug("stopReceiving() | calling pc.setLocalDescription() [answer:%o]",r),await this._pc.setLocalDescription(r);for(const t of e)this._mapMidTransceiver.delete(t)}async pauseReceiving(e){this.assertNotClosed(),this.assertRecvDirection();for(const t of e){h.debug("pauseReceiving() [localId:%s]",t);const e=this._mapMidTransceiver.get(t);if(!e)throw new Error("associated RTCRtpTransceiver not found");e.direction="inactive",this._remoteSdp.pauseMediaSection(t)}const t={type:"offer",sdp:this._remoteSdp.getSdp()};h.debug("pauseReceiving() | calling pc.setRemoteDescription() [offer:%o]",t),await this._pc.setRemoteDescription(t);const r=await this._pc.createAnswer();h.debug("pauseReceiving() | calling pc.setLocalDescription() [answer:%o]",r),await this._pc.setLocalDescription(r)}async resumeReceiving(e){this.assertNotClosed(),this.assertRecvDirection();for(const t of e){h.debug("resumeReceiving() [localId:%s]",t);const e=this._mapMidTransceiver.get(t);if(!e)throw new Error("associated RTCRtpTransceiver not found");e.direction="recvonly",this._remoteSdp.resumeReceivingMediaSection(t)}const t={type:"offer",sdp:this._remoteSdp.getSdp()};h.debug("resumeReceiving() | calling pc.setRemoteDescription() [offer:%o]",t),await this._pc.setRemoteDescription(t);const r=await this._pc.createAnswer();h.debug("resumeReceiving() | calling pc.setLocalDescription() [answer:%o]",r),await this._pc.setLocalDescription(r)}async getReceiverStats(e){this.assertRecvDirection();const t=this._mapMidTransceiver.get(e);if(!t)throw new Error("associated RTCRtpTransceiver not found");return t.receiver.getStats()}async receiveDataChannel({sctpStreamParameters:e,label:t,protocol:r}){this.assertNotClosed(),this.assertRecvDirection();const{streamId:i,ordered:a,maxPacketLifeTime:n,maxRetransmits:o}=e,c={negotiated:!0,id:i,ordered:a,maxPacketLifeTime:n,maxRetransmits:o,protocol:r};h.debug("receiveDataChannel() [options:%o]",c);const d=this._pc.createDataChannel(t,c);if(!this._hasDataChannelMediaSection){this._remoteSdp.receiveSctpAssociation();const e={type:"offer",sdp:this._remoteSdp.getSdp()};h.debug("receiveDataChannel() | calling pc.setRemoteDescription() [offer:%o]",e),await this._pc.setRemoteDescription(e);const t=await this._pc.createAnswer();if(!this._transportReady){const e=s.parse(t.sdp);await this.setupTransport({localDtlsRole:"client",localSdpObject:e})}h.debug("receiveDataChannel() | calling pc.setRemoteDescription() [answer:%o]",t),await this._pc.setLocalDescription(t),this._hasDataChannelMediaSection=!0}return{dataChannel:d}}async setupTransport({localDtlsRole:e,localSdpObject:t}){t||(t=s.parse(this._pc.localDescription.sdp));const r=d.extractDtlsParameters({sdpObject:t});r.role=e,this._remoteSdp.updateDtlsRole("client"===e?"server":"client"),await new Promise((e,t)=>{this.safeEmit("@connect",{dtlsParameters:r},e,t)}),this._transportReady=!0}onIceGatheringStateChange=()=>{this.emit("@icegatheringstatechange",this._pc.iceGatheringState)};onIceCandidateError=e=>{this.emit("@icecandidateerror",e)};onConnectionStateChange=()=>{this.emit("@connectionstatechange",this._pc.connectionState)};onIceConnectionStateChange=()=>{switch(this._pc.iceConnectionState){case"checking":this.emit("@connectionstatechange","connecting");break;case"connected":case"completed":this.emit("@connectionstatechange","connected");break;case"failed":this.emit("@connectionstatechange","failed");break;case"disconnected":this.emit("@connectionstatechange","disconnected");break;case"closed":this.emit("@connectionstatechange","closed")}};assertNotClosed(){if(this._closed)throw new n.InvalidStateError("method called in a closed handler")}assertSendDirection(){if("send"!==this._direction)throw new Error('method can just be called for handlers with "send" direction')}assertRecvDirection(){if("recv"!==this._direction)throw new Error('method can just be called for handlers with "recv" direction')}}t.Firefox120=f},2731:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.FakeHandler=void 0;const s=r(5328),i=r(3953),a=r(2994),n=r(1765),o=r(8046),c=r(4893),d=new a.Logger("FakeHandler"),p="FakeHandler";class l extends i.EnhancedEventEmitter{_closed=!1;_fakeParameters;_getSendExtendedRtpCapabilities;_cname=`CNAME-${n.generateRandomNumber()}`;_transportReady=!1;_nextLocalId=1;_tracks=new Map;_nextSctpStreamId=0;static createFactory(e){return{name:p,factory:t=>new l(t,e),getNativeRtpCapabilities:async()=>(d.debug("getNativeRtpCapabilities()"),l.getLocalRtpCapabilities(e)),getNativeSctpCapabilities:async()=>(d.debug("getNativeSctpCapabilities()"),e.generateNativeSctpCapabilities())}}static getLocalRtpCapabilities(e){return e.generateNativeRtpCapabilities()}constructor({getSendExtendedRtpCapabilities:e},t){super(),d.debug("constructor()"),this._getSendExtendedRtpCapabilities=e,this._fakeParameters=t}get name(){return p}close(){d.debug("close()"),this._closed||(this._closed=!0,super.close())}setIceGatheringState(e){this.emit("@icegatheringstatechange",e)}setConnectionState(e){this.emit("@connectionstatechange",e)}async updateIceServers(e){this.assertNotClosed(),d.debug("updateIceServers()")}async restartIce(e){this.assertNotClosed(),d.debug("restartIce()")}async getTransportStats(){return this.assertNotClosed(),new Map}async send({track:e,encodings:t,codecOptions:r,codec:s}){this.assertNotClosed(),d.debug("send() [kind:%s, track.id:%s]",e.kind,e.id),this._transportReady||await this.setupTransport({localDtlsRole:"server"});const i=l.getLocalRtpCapabilities(this._fakeParameters),a=this._getSendExtendedRtpCapabilities(i),c=o.getSendingRtpParameters(e.kind,a);c.codecs=o.reduceCodecs(c.codecs,s);const p=c.codecs.some(e=>/.+\/rtx$/i.test(e.mimeType));c.mid=`mid-${n.generateRandomNumber()}`,t||(t=[{}]);for(const e of t)e.ssrc=n.generateRandomNumber(),p&&(e.rtx={ssrc:n.generateRandomNumber()});c.encodings=t,c.rtcp={cname:this._cname,reducedSize:!0,mux:!0};const h=this._nextLocalId++;return this._tracks.set(h,e),{localId:String(h),rtpParameters:c}}async stopSending(e){if(d.debug("stopSending() [localId:%s]",e),!this._closed){if(!this._tracks.has(Number(e)))throw new Error("local track not found");this._tracks.delete(Number(e))}}async pauseSending(e){this.assertNotClosed()}async resumeSending(e){this.assertNotClosed()}async replaceTrack(e,t){this.assertNotClosed(),t?d.debug("replaceTrack() [localId:%s, track.id:%s]",e,t.id):d.debug("replaceTrack() [localId:%s, no track]",e),this._tracks.delete(Number(e)),this._tracks.set(Number(e),t)}async setMaxSpatialLayer(e,t){this.assertNotClosed(),d.debug("setMaxSpatialLayer() [localId:%s, spatialLayer:%s]",e,t)}async setRtpEncodingParameters(e,t){this.assertNotClosed(),d.debug("setRtpEncodingParameters() [localId:%s, params:%o]",e,t)}async getSenderStats(e){return this.assertNotClosed(),new Map}async sendDataChannel({ordered:e,maxPacketLifeTime:t,maxRetransmits:r,label:s,protocol:i}){return this.assertNotClosed(),this._transportReady||await this.setupTransport({localDtlsRole:"server"}),d.debug("sendDataChannel()"),{dataChannel:new h({id:this._nextSctpStreamId++,ordered:e,maxPacketLifeTime:t,maxRetransmits:r,label:s,protocol:i}),sctpStreamParameters:{streamId:this._nextSctpStreamId,ordered:e,maxPacketLifeTime:t,maxRetransmits:r}}}async receive(e){this.assertNotClosed();const t=[];for(const r of e){const{trackId:e,kind:i}=r;this._transportReady||await this.setupTransport({localDtlsRole:"client"}),d.debug("receive() [trackId:%s, kind:%s]",e,i);const a=this._nextLocalId++,n=new s.FakeMediaStreamTrack({kind:i});this._tracks.set(a,n),t.push({localId:String(a),track:n})}return t}async stopReceiving(e){if(!this._closed)for(const t of e)d.debug("stopReceiving() [localId:%s]",t),this._tracks.delete(Number(t))}async pauseReceiving(e){this.assertNotClosed()}async resumeReceiving(e){this.assertNotClosed()}async getReceiverStats(e){return this.assertNotClosed(),new Map}async receiveDataChannel({sctpStreamParameters:e,label:t,protocol:r}){return this.assertNotClosed(),this._transportReady||await this.setupTransport({localDtlsRole:"client"}),d.debug("receiveDataChannel()"),{dataChannel:new h({id:e.streamId,ordered:e.ordered,maxPacketLifeTime:e.maxPacketLifeTime,maxRetransmits:e.maxRetransmits,label:t,protocol:r})}}async setupTransport({localDtlsRole:e,localSdpObject:t}){const r=n.clone(this._fakeParameters.generateLocalDtlsParameters());e&&(r.role=e),this.emit("@connectionstatechange","connecting"),await new Promise((e,t)=>this.emit("@connect",{dtlsParameters:r},e,t)),this._transportReady=!0}assertNotClosed(){if(this._closed)throw new c.InvalidStateError("method called in a closed handler")}}t.FakeHandler=l;class h extends EventTarget{_id;_negotiated=!0;_ordered;_maxPacketLifeTime;_maxRetransmits;_label;_protocol;_readyState="connecting";_bufferedAmount=0;_bufferedAmountLowThreshold=0;_binaryType="arraybuffer";_onopen=null;_onclosing=null;_onclose=null;_onmessage=null;_onbufferedamountlow=null;_onerror=null;constructor({id:e,ordered:t=!0,maxPacketLifeTime:r=null,maxRetransmits:s=null,label:i="",protocol:a=""}){super(),d.debug(`constructor() [id:${e}, ordered:${t}, maxPacketLifeTime:${r}, maxRetransmits:${s}, label:${i}, protocol:${a}`),this._id=e,this._ordered=t,this._maxPacketLifeTime=r,this._maxRetransmits=s,this._label=i,this._protocol=a}get id(){return this._id}get negotiated(){return this._negotiated}get ordered(){return this._ordered}get maxPacketLifeTime(){return this._maxPacketLifeTime}get maxRetransmits(){return this._maxRetransmits}get label(){return this._label}get protocol(){return this._protocol}get readyState(){return this._readyState}get bufferedAmount(){return this._bufferedAmount}get bufferedAmountLowThreshold(){return this._bufferedAmountLowThreshold}set bufferedAmountLowThreshold(e){this._bufferedAmountLowThreshold=e}get binaryType(){return this._binaryType}set binaryType(e){this._binaryType=e}get onopen(){return this._onopen}set onopen(e){this._onopen&&this.removeEventListener("open",this._onopen),this._onopen=e,e&&this.addEventListener("open",e)}get onclosing(){return this._onclosing}set onclosing(e){this._onclosing&&this.removeEventListener("closing",this._onclosing),this._onclosing=e,e&&this.addEventListener("closing",e)}get onclose(){return this._onclose}set onclose(e){this._onclose&&this.removeEventListener("close",this._onclose),this._onclose=e,e&&this.addEventListener("close",e)}get onmessage(){return this._onmessage}set onmessage(e){this._onmessage&&this.removeEventListener("message",this._onmessage),this._onmessage=e,e&&this.addEventListener("message",e)}get onbufferedamountlow(){return this._onbufferedamountlow}set onbufferedamountlow(e){this._onbufferedamountlow&&this.removeEventListener("bufferedamountlow",this._onbufferedamountlow),this._onbufferedamountlow=e,e&&this.addEventListener("bufferedamountlow",e)}get onerror(){return this._onerror}set onerror(e){this._onerror&&this.removeEventListener("error",this._onerror),this._onerror=e,e&&this.addEventListener("error",e)}addEventListener(e,t,r){super.addEventListener(e,t,r)}removeEventListener(e,t,r){super.removeEventListener(e,t,r)}close(){["closing","closed"].includes(this._readyState)||(this._readyState="closed")}send(e){if("open"!==this._readyState)throw new c.InvalidStateError("not open")}}},2770:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const s=r(9746);t.default=function(e){if(!(0,s.default)(e))throw TypeError("Invalid UUID");return parseInt(e.slice(14,15),16)}},2829:(e,t)=>{"use strict";function r(e,t,r,s){switch(e){case 0:return t&r^~t&s;case 1:case 3:return t^r^s;case 2:return t&r^t&s^r&s}}function s(e,t){return e<<t|e>>>32-t}Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){const t=[1518500249,1859775393,2400959708,3395469782],i=[1732584193,4023233417,2562383102,271733878,3285377520],a=new Uint8Array(e.length+1);a.set(e),a[e.length]=128;const n=(e=a).length/4+2,o=Math.ceil(n/16),c=new Array(o);for(let t=0;t<o;++t){const r=new Uint32Array(16);for(let s=0;s<16;++s)r[s]=e[64*t+4*s]<<24|e[64*t+4*s+1]<<16|e[64*t+4*s+2]<<8|e[64*t+4*s+3];c[t]=r}c[o-1][14]=8*(e.length-1)/Math.pow(2,32),c[o-1][14]=Math.floor(c[o-1][14]),c[o-1][15]=8*(e.length-1)&4294967295;for(let e=0;e<o;++e){const a=new Uint32Array(80);for(let t=0;t<16;++t)a[t]=c[e][t];for(let e=16;e<80;++e)a[e]=s(a[e-3]^a[e-8]^a[e-14]^a[e-16],1);let n=i[0],o=i[1],d=i[2],p=i[3],l=i[4];for(let e=0;e<80;++e){const i=Math.floor(e/20),c=s(n,5)+r(i,o,d,p)+l+t[i]+a[e]>>>0;l=p,p=d,d=s(o,30)>>>0,o=n,n=c}i[0]=i[0]+n>>>0,i[1]=i[1]+o>>>0,i[2]=i[2]+d>>>0,i[3]=i[3]+p>>>0,i[4]=i[4]+l>>>0}return Uint8Array.of(i[0]>>24,i[0]>>16,i[0]>>8,i[0],i[1]>>24,i[1]>>16,i[1]>>8,i[1],i[2]>>24,i[2]>>16,i[2]>>8,i[2],i[3]>>24,i[3]>>16,i[3]>>8,i[3],i[4]>>24,i[4]>>16,i[4]>>8,i[4])}},2988:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.URL=t.DNS=t.stringToBytes=void 0;const s=r(1797),i=r(6011);function a(e){e=unescape(encodeURIComponent(e));const t=new Uint8Array(e.length);for(let r=0;r<e.length;++r)t[r]=e.charCodeAt(r);return t}t.stringToBytes=a,t.DNS="6ba7b810-9dad-11d1-80b4-00c04fd430c8",t.URL="6ba7b811-9dad-11d1-80b4-00c04fd430c8",t.default=function(e,t,r,n,o,c){const d="string"==typeof r?a(r):r,p="string"==typeof n?(0,s.default)(n):n;if("string"==typeof n&&(n=(0,s.default)(n)),16!==n?.length)throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");let l=new Uint8Array(16+d.length);if(l.set(p),l.set(d,p.length),l=t(l),l[6]=15&l[6]|e,l[8]=63&l[8]|128,o){c=c||0;for(let e=0;e<16;++e)o[c+e]=l[e];return o}return(0,i.unsafeStringify)(l)}},2994:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Logger=void 0;const s=r(7833),i="mediasoup-client";t.Logger=class{_debug;_warn;_error;constructor(e){e?(this._debug=(0,s.default)(`${i}:${e}`),this._warn=(0,s.default)(`${i}:WARN:${e}`),this._error=(0,s.default)(`${i}:ERROR:${e}`)):(this._debug=(0,s.default)(i),this._warn=(0,s.default)(`${i}:WARN`),this._error=(0,s.default)(`${i}:ERROR`)),this._debug.log=console.info.bind(console),this._warn.log=console.warn.bind(console),this._error.log=console.error.bind(console)}get debug(){return this._debug}get warn(){return this._warn}get error(){return this._error}}},3200:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ProfileLevelId=t.Level=t.Profile=void 0,t.parseProfileLevelId=h,t.profileLevelIdToString=u,t.profileToString=function(e){switch(e){case i.ConstrainedBaseline:return"ConstrainedBaseline";case i.Baseline:return"Baseline";case i.Main:return"Main";case i.ConstrainedHigh:return"ConstrainedHigh";case i.High:return"High";case i.PredictiveHigh444:return"PredictiveHigh444";default:return void s.warn(`profileToString() | unrecognized profile ${e}`)}},t.levelToString=function(e){switch(e){case a.L1_b:return"1b";case a.L1:return"1";case a.L1_1:return"1.1";case a.L1_2:return"1.2";case a.L1_3:return"1.3";case a.L2:return"2";case a.L2_1:return"2.1";case a.L2_2:return"2.2";case a.L3:return"3";case a.L3_1:return"3.1";case a.L3_2:return"3.2";case a.L4:return"4";case a.L4_1:return"4.1";case a.L4_2:return"4.2";case a.L5:return"5";case a.L5_1:return"5.1";case a.L5_2:return"5.2";default:return void s.warn(`levelToString() | unrecognized level ${e}`)}},t.parseSdpProfileLevelId=m,t.isSameProfile=function(e={},t={}){const r=m(e),s=m(t);return Boolean(r&&s&&r.profile===s.profile)},t.isSameProfileAndLevel=function(e={},t={}){const r=m(e),s=m(t);return Boolean(r&&s&&r.profile===s.profile&&r.level==s.level)},t.generateProfileLevelIdStringForAnswer=function(e={},t={}){if(!e["profile-level-id"]&&!t["profile-level-id"])return void s.warn("generateProfileLevelIdStringForAnswer() | profile-level-id missing in local and remote params");const r=m(e),i=m(t);if(!r)throw new TypeError("invalid local_profile_level_id");if(!i)throw new TypeError("invalid remote_profile_level_id");if(r.profile!==i.profile)throw new TypeError("H264 Profile mismatch");const o=g(e)&&g(t),c=r.level,d=function(e,t){return e===a.L1_b?t!==a.L1&&t!==a.L1_b:t===a.L1_b?e!==a.L1:e<t}(l=c,h=i.level)?l:h,p=o?c:d;var l,h;return s.debug(`generateProfileLevelIdStringForAnswer() | result [profile:${r.profile}, level:${p}]`),u(new n(r.profile,p))},t.supportedLevel=function(e,t){for(let r=l.length-1;r>=0;--r){const i=l[r];if(256*i.max_macroblock_frame_size<=e&&i.max_macroblocks_per_second<=t*i.max_macroblock_frame_size)return s.debug(`supportedLevel() | result [max_frame_pixel_count:${e}, max_fps:${t}, level:${i.level}]`),i.level}s.warn(`supportedLevel() | no level supported [max_frame_pixel_count:${e}, max_fps:${t}]`)};const s=new(r(3582).Logger);var i,a;!function(e){e[e.ConstrainedBaseline=1]="ConstrainedBaseline",e[e.Baseline=2]="Baseline",e[e.Main=3]="Main",e[e.ConstrainedHigh=4]="ConstrainedHigh",e[e.High=5]="High",e[e.PredictiveHigh444=6]="PredictiveHigh444"}(i||(t.Profile=i={})),function(e){e[e.L1_b=0]="L1_b",e[e.L1=10]="L1",e[e.L1_1=11]="L1_1",e[e.L1_2=12]="L1_2",e[e.L1_3=13]="L1_3",e[e.L2=20]="L2",e[e.L2_1=21]="L2_1",e[e.L2_2=22]="L2_2",e[e.L3=30]="L3",e[e.L3_1=31]="L3_1",e[e.L3_2=32]="L3_2",e[e.L4=40]="L4",e[e.L4_1=41]="L4_1",e[e.L4_2=42]="L4_2",e[e.L5=50]="L5",e[e.L5_1=51]="L5_1",e[e.L5_2=52]="L5_2"}(a||(t.Level=a={}));class n{constructor(e,t){this.profile=e,this.level=t}}t.ProfileLevelId=n;const o=new n(i.ConstrainedBaseline,a.L3_1);class c{constructor(e){this.mask=~f("x",e),this.masked_value=f("1",e)}isMatch(e){return this.masked_value===(e&this.mask)}}class d{constructor(e,t,r){this.profile_idc=e,this.profile_iop=t,this.profile=r}}const p=[new d(66,new c("x1xx0000"),i.ConstrainedBaseline),new d(77,new c("1xxx0000"),i.ConstrainedBaseline),new d(88,new c("11xx0000"),i.ConstrainedBaseline),new d(66,new c("x0xx0000"),i.Baseline),new d(88,new c("10xx0000"),i.Baseline),new d(77,new c("0x0x0000"),i.Main),new d(100,new c("00000000"),i.High),new d(100,new c("00001100"),i.ConstrainedHigh),new d(244,new c("00000000"),i.PredictiveHigh444)],l=[{max_macroblocks_per_second:1485,max_macroblock_frame_size:99,level:a.L1},{max_macroblocks_per_second:1485,max_macroblock_frame_size:99,level:a.L1_b},{max_macroblocks_per_second:3e3,max_macroblock_frame_size:396,level:a.L1_1},{max_macroblocks_per_second:6e3,max_macroblock_frame_size:396,level:a.L1_2},{max_macroblocks_per_second:11880,max_macroblock_frame_size:396,level:a.L1_3},{max_macroblocks_per_second:11880,max_macroblock_frame_size:396,level:a.L2},{max_macroblocks_per_second:19800,max_macroblock_frame_size:792,level:a.L2_1},{max_macroblocks_per_second:20250,max_macroblock_frame_size:1620,level:a.L2_2},{max_macroblocks_per_second:40500,max_macroblock_frame_size:1620,level:a.L3},{max_macroblocks_per_second:108e3,max_macroblock_frame_size:3600,level:a.L3_1},{max_macroblocks_per_second:216e3,max_macroblock_frame_size:5120,level:a.L3_2},{max_macroblocks_per_second:245760,max_macroblock_frame_size:8192,level:a.L4},{max_macroblocks_per_second:245760,max_macroblock_frame_size:8192,level:a.L4_1},{max_macroblocks_per_second:522240,max_macroblock_frame_size:8704,level:a.L4_2},{max_macroblocks_per_second:589824,max_macroblock_frame_size:22080,level:a.L5},{max_macroblocks_per_second:983040,max_macroblock_frame_size:36864,level:a.L5_1},{max_macroblocks_per_second:2073600,max_macroblock_frame_size:36864,level:a.L5_2}];function h(e){if("string"!=typeof e||6!==e.length)return;const t=parseInt(e,16);if(0===t)return;const r=255&t,i=t>>8&255,o=t>>16&255;let c;switch(r){case a.L1_1:c=16&i?a.L1_b:a.L1_1;break;case a.L1:case a.L1_2:case a.L1_3:case a.L2:case a.L2_1:case a.L2_2:case a.L3:case a.L3_1:case a.L3_2:case a.L4:case a.L4_1:case a.L4_2:case a.L5:case a.L5_1:case a.L5_2:c=r;break;default:return void s.warn(`parseProfileLevelId() | unrecognized level_idc [str:${e}, level_idc:${r}]`)}for(const t of p)if(o===t.profile_idc&&t.profile_iop.isMatch(i))return s.debug(`parseProfileLevelId() | result [str:${e}, profile:${t.profile}, level:${c}]`),new n(t.profile,c);s.warn(`parseProfileLevelId() | unrecognized profile_idc/profile_iop combination [str:${e}, profile_idc:${o}, profile_iop:${i}]`)}function u(e){if(e.level==a.L1_b)switch(e.profile){case i.ConstrainedBaseline:return"42f00b";case i.Baseline:return"42100b";case i.Main:return"4d100b";default:return void s.warn(`profileLevelIdToString() | Level 1_b not is allowed for profile ${e.profile}`)}let t;switch(e.profile){case i.ConstrainedBaseline:t="42e0";break;case i.Baseline:t="4200";break;case i.Main:t="4d00";break;case i.ConstrainedHigh:t="640c";break;case i.High:t="6400";break;case i.PredictiveHigh444:t="f400";break;default:return void s.warn(`profileLevelIdToString() | unrecognized profile ${e.profile}`)}let r=e.level.toString(16);return 1===r.length&&(r=`0${r}`),`${t}${r}`}function m(e={}){const t=e["profile-level-id"];return t?h(t):o}function f(e,t){return Number(t[0]===e)<<7|Number(t[1]===e)<<6|Number(t[2]===e)<<5|Number(t[3]===e)<<4|Number(t[4]===e)<<3|Number(t[5]===e)<<2|Number(t[6]===e)<<1|Number(t[7]===e)}function g(e={}){const t=e["level-asymmetry-allowed"];return!0===t||1===t||"1"===t}},3303:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.parse=function(e){const t=r.exec(e??"");return t?{spatialLayers:Number(t[1]),temporalLayers:Number(t[2])}:{spatialLayers:1,temporalLayers:1}};const r=new RegExp("^[LS]([1-9]\\d{0,1})T([1-9]\\d{0,1})")},3465:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default="00000000-0000-0000-0000-000000000000"},3471:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.OfferMediaSection=t.AnswerMediaSection=t.MediaSection=void 0;const s=r(7363),i=r(1765);class a{_mediaObject;constructor({iceParameters:e,iceCandidates:t,dtlsParameters:r}){if(this._mediaObject={type:"",port:0,protocol:"",payloads:"",rtp:[],fmtp:[]},e&&this.setIceParameters(e),t){this._mediaObject.candidates=[];for(const e of t){const t={foundation:e.foundation,component:1,ip:e.address??e.ip,port:e.port,priority:e.priority,transport:e.protocol,type:e.type};e.tcpType&&(t.tcptype=e.tcpType),this._mediaObject.candidates.push(t)}this._mediaObject.endOfCandidates="end-of-candidates",this._mediaObject.iceOptions="renomination"}r&&this.setDtlsRole(r.role)}get mid(){return String(this._mediaObject.mid)}get closed(){return 0===this._mediaObject.port}getObject(){return this._mediaObject}setIceParameters(e){this._mediaObject.iceUfrag=e.usernameFragment,this._mediaObject.icePwd=e.password}pause(){this._mediaObject.direction="inactive"}disable(){this.pause(),delete this._mediaObject.ext,delete this._mediaObject.ssrcs,delete this._mediaObject.ssrcGroups,delete this._mediaObject.simulcast,delete this._mediaObject.simulcast_03,delete this._mediaObject.rids,delete this._mediaObject.extmapAllowMixed}close(){this.disable(),this._mediaObject.port=0}}function n(e){const t=new RegExp("^(audio|video)/(.+)","i").exec(e.mimeType);if(!t)throw new TypeError("invalid codec.mimeType");return t[2]}t.MediaSection=a,t.AnswerMediaSection=class extends a{constructor({iceParameters:e,iceCandidates:t,dtlsParameters:r,sctpParameters:s,plainRtpParameters:a,offerMediaObject:o,offerRtpParameters:c,answerRtpParameters:d,codecOptions:p}){switch(super({iceParameters:e,iceCandidates:t,dtlsParameters:r}),this._mediaObject.mid=String(o.mid),this._mediaObject.type=o.type,this._mediaObject.protocol=o.protocol,a?(this._mediaObject.connection={ip:a.ip,version:a.ipVersion},this._mediaObject.port=a.port):(this._mediaObject.connection={ip:"127.0.0.1",version:4},this._mediaObject.port=7),o.type){case"audio":case"video":this._mediaObject.direction="recvonly",this._mediaObject.rtp=[],this._mediaObject.rtcpFb=[],this._mediaObject.fmtp=[];for(const e of d.codecs){const t={payload:e.payloadType,codec:n(e),rate:e.clockRate};e.channels>1&&(t.encoding=e.channels),this._mediaObject.rtp.push(t);const r=i.clone(e.parameters)??{};let s=i.clone(e.rtcpFeedback)??[];if(p){const{opusStereo:t,opusFec:i,opusDtx:a,opusMaxPlaybackRate:n,opusMaxAverageBitrate:o,opusPtime:d,opusNack:l,videoGoogleStartBitrate:h,videoGoogleMaxBitrate:u,videoGoogleMinBitrate:m}=p,f=c.codecs.find(t=>t.payloadType===e.payloadType);switch(e.mimeType.toLowerCase()){case"audio/opus":case"audio/multiopus":void 0!==t&&(f.parameters["sprop-stereo"]=t?1:0,r.stereo=t?1:0),void 0!==i&&(f.parameters.useinbandfec=i?1:0,r.useinbandfec=i?1:0),void 0!==a&&(f.parameters.usedtx=a?1:0,r.usedtx=a?1:0),void 0!==n&&(r.maxplaybackrate=n),void 0!==o&&(r.maxaveragebitrate=o),void 0!==d&&(f.parameters.ptime=d,r.ptime=d),l||(f.rtcpFeedback=f.rtcpFeedback.filter(e=>"nack"!==e.type||e.parameter),s=s.filter(e=>"nack"!==e.type||e.parameter));break;case"video/vp8":case"video/vp9":case"video/h264":case"video/h265":case"video/av1":void 0!==h&&(r["x-google-start-bitrate"]=h),void 0!==u&&(r["x-google-max-bitrate"]=u),void 0!==m&&(r["x-google-min-bitrate"]=m)}}const a={payload:e.payloadType,config:""};for(const e of Object.keys(r))a.config&&(a.config+=";"),a.config+=`${e}=${r[e]}`;a.config&&this._mediaObject.fmtp.push(a);for(const t of s)this._mediaObject.rtcpFb.push({payload:e.payloadType,type:t.type,subtype:t.parameter})}this._mediaObject.payloads=d.codecs.map(e=>e.payloadType).join(" "),this._mediaObject.ext=[];for(const e of d.headerExtensions)(o.ext??[]).some(t=>t.uri===e.uri)&&this._mediaObject.ext.push({uri:e.uri,value:e.id});if("extmap-allow-mixed"===o.extmapAllowMixed&&(this._mediaObject.extmapAllowMixed="extmap-allow-mixed"),o.simulcast){this._mediaObject.simulcast={dir1:"recv",list1:o.simulcast.list1},this._mediaObject.rids=[];for(const e of o.rids??[])"send"===e.direction&&this._mediaObject.rids.push({id:e.id,direction:"recv"})}else if(o.simulcast_03){this._mediaObject.simulcast_03={value:o.simulcast_03.value.replace(/send/g,"recv")},this._mediaObject.rids=[];for(const e of o.rids??[])"send"===e.direction&&this._mediaObject.rids.push({id:e.id,direction:"recv"})}this._mediaObject.rtcpMux="rtcp-mux",this._mediaObject.rtcpRsize="rtcp-rsize";break;case"application":"number"==typeof o.sctpPort?(this._mediaObject.payloads="webrtc-datachannel",this._mediaObject.sctpPort=s.port,this._mediaObject.maxMessageSize=s.maxMessageSize):o.sctpmap&&(this._mediaObject.payloads=String(s.port),this._mediaObject.sctpmap={app:"webrtc-datachannel",sctpmapNumber:s.port,maxMessageSize:s.maxMessageSize})}}setDtlsRole(e){switch(e){case"client":this._mediaObject.setup="active";break;case"server":this._mediaObject.setup="passive";break;case"auto":this._mediaObject.setup="actpass"}}resume(){this._mediaObject.direction="recvonly"}muxSimulcastStreams(e){if(!this._mediaObject.simulcast?.list1)return;const t={};for(const r of e)r.rid&&(t[r.rid]=r);const r=this._mediaObject.simulcast.list1,i=s.parseSimulcastStreamList(r);for(const e of i)for(const r of e)r.paused=!t[r.scid]?.active;this._mediaObject.simulcast.list1=i.map(e=>e.map(e=>`${e.paused?"~":""}${e.scid}`).join(",")).join(";")}},t.OfferMediaSection=class extends a{constructor({iceParameters:e,iceCandidates:t,dtlsParameters:r,sctpParameters:s,plainRtpParameters:i,mid:a,kind:o,offerRtpParameters:c,streamId:d,trackId:p}){switch(super({iceParameters:e,iceCandidates:t,dtlsParameters:r}),this._mediaObject.mid=String(a),this._mediaObject.type=o,i?(this._mediaObject.connection={ip:i.ip,version:i.ipVersion},this._mediaObject.protocol="RTP/AVP",this._mediaObject.port=i.port):(this._mediaObject.connection={ip:"127.0.0.1",version:4},this._mediaObject.protocol=s?"UDP/DTLS/SCTP":"UDP/TLS/RTP/SAVPF",this._mediaObject.port=7),this._mediaObject.extmapAllowMixed="extmap-allow-mixed",o){case"audio":case"video":{this._mediaObject.direction="sendonly",this._mediaObject.rtp=[],this._mediaObject.rtcpFb=[],this._mediaObject.fmtp=[],this._mediaObject.msid=`${d??"-"} ${p}`;for(const e of c.codecs){const t={payload:e.payloadType,codec:n(e),rate:e.clockRate};e.channels>1&&(t.encoding=e.channels),this._mediaObject.rtp.push(t);const r={payload:e.payloadType,config:""};for(const t of Object.keys(e.parameters??{}))r.config&&(r.config+=";"),r.config+=`${t}=${e.parameters[t]}`;r.config&&this._mediaObject.fmtp.push(r);for(const t of e.rtcpFeedback)this._mediaObject.rtcpFb.push({payload:e.payloadType,type:t.type,subtype:t.parameter})}this._mediaObject.payloads=c.codecs.map(e=>e.payloadType).join(" "),this._mediaObject.ext=[];for(const e of c.headerExtensions)this._mediaObject.ext.push({uri:e.uri,value:e.id});this._mediaObject.rtcpMux="rtcp-mux",this._mediaObject.rtcpRsize="rtcp-rsize";const e=c.encodings[0],t=e.ssrc,r=e.rtx?.ssrc;this._mediaObject.ssrcs=[],this._mediaObject.ssrcGroups=[],t&&c.rtcp.cname&&this._mediaObject.ssrcs.push({id:t,attribute:"cname",value:c.rtcp.cname}),r&&(c.rtcp.cname&&this._mediaObject.ssrcs.push({id:r,attribute:"cname",value:c.rtcp.cname}),t&&this._mediaObject.ssrcGroups.push({semantics:"FID",ssrcs:`${t} ${r}`}));break}case"application":this._mediaObject.payloads="webrtc-datachannel",this._mediaObject.sctpPort=s.port,this._mediaObject.maxMessageSize=s.maxMessageSize}}setDtlsRole(e){this._mediaObject.setup="actpass"}resume(){this._mediaObject.direction="sendonly"}}},3518:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Consumer=void 0;const s=r(2994),i=r(3953),a=r(4893),n=new s.Logger("Consumer");class o extends i.EnhancedEventEmitter{_id;_localId;_producerId;_closed=!1;_rtpReceiver;_track;_rtpParameters;_paused;_appData;_observer=new i.EnhancedEventEmitter;constructor({id:e,localId:t,producerId:r,rtpReceiver:s,track:i,rtpParameters:a,appData:o}){super(),n.debug("constructor()"),this._id=e,this._localId=t,this._producerId=r,this._rtpReceiver=s,this._track=i,this._rtpParameters=a,this._paused=!i.enabled,this._appData=o??{},this.onTrackEnded=this.onTrackEnded.bind(this),this.handleTrack()}get id(){return this._id}get localId(){return this._localId}get producerId(){return this._producerId}get closed(){return this._closed}get kind(){return this._track.kind}get rtpReceiver(){return this._rtpReceiver}get track(){return this._track}get rtpParameters(){return this._rtpParameters}get paused(){return this._paused}get appData(){return this._appData}set appData(e){this._appData=e}get observer(){return this._observer}close(){this._closed||(n.debug("close()"),this._closed=!0,this.destroyTrack(),this.emit("@close"),this._observer.safeEmit("close"),super.close(),this._observer.close())}transportClosed(){this._closed||(n.debug("transportClosed()"),this._closed=!0,this.destroyTrack(),this.safeEmit("transportclose"),this._observer.safeEmit("close"))}async getStats(){if(this._closed)throw new a.InvalidStateError("closed");return new Promise((e,t)=>{this.safeEmit("@getstats",e,t)})}pause(){n.debug("pause()"),this._closed?n.error("pause() | Consumer closed"):this._paused?n.debug("pause() | Consumer is already paused"):(this._paused=!0,this._track.enabled=!1,this.emit("@pause"),this._observer.safeEmit("pause"))}resume(){n.debug("resume()"),this._closed?n.error("resume() | Consumer closed"):this._paused?(this._paused=!1,this._track.enabled=!0,this.emit("@resume"),this._observer.safeEmit("resume")):n.debug("resume() | Consumer is already resumed")}onTrackEnded(){n.debug('track "ended" event'),this.safeEmit("trackended"),this._observer.safeEmit("trackended")}handleTrack(){this._track.addEventListener("ended",this.onTrackEnded)}destroyTrack(){try{this._track.removeEventListener("ended",this.onTrackEnded),this._track.stop()}catch(e){}}}t.Consumer=o},3582:function(e,t,r){"use strict";var s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.Logger=void 0;const i=s(r(7833)),a="h264-profile-level-id";t.Logger=class{constructor(e){e?(this._debug=(0,i.default)(`${a}:${e}`),this._warn=(0,i.default)(`${a}:WARN:${e}`),this._error=(0,i.default)(`${a}:ERROR:${e}`)):(this._debug=(0,i.default)(a),this._warn=(0,i.default)(`${a}:WARN`),this._error=(0,i.default)(`${a}:ERROR`)),this._debug.log=console.info.bind(console),this._warn.log=console.warn.bind(console),this._error.log=console.error.bind(console)}get debug(){return this._debug}get warn(){return this._warn}get error(){return this._error}}},3779:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r="undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto);t.default={randomUUID:r}},3785:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.clone=function(e){return void 0===e?void 0:Number.isNaN(e)?NaN:"function"==typeof structuredClone?structuredClone(e):JSON.parse(JSON.stringify(e))}},3804:(e,t,r)=>{var s=r(5602),i=/%[sdv%]/g,a=function(e){var t=1,r=arguments,s=r.length;return e.replace(i,function(e){if(t>=s)return e;var i=r[t];switch(t+=1,e){case"%%":return"%";case"%s":return String(i);case"%d":return Number(i);case"%v":return""}})},n=function(e,t,r){var s=[e+"="+(t.format instanceof Function?t.format(t.push?r:r[t.name]):t.format)];if(t.names)for(var i=0;i<t.names.length;i+=1){var n=t.names[i];t.name?s.push(r[t.name][n]):s.push(r[t.names[i]])}else s.push(r[t.name]);return a.apply(null,s)},o=["v","o","s","i","u","e","p","c","b","t","r","z","a"],c=["i","c","b","a"];e.exports=function(e,t){t=t||{},null==e.version&&(e.version=0),null==e.name&&(e.name=" "),e.media.forEach(function(e){null==e.payloads&&(e.payloads="")});var r=t.outerOrder||o,i=t.innerOrder||c,a=[];return r.forEach(function(t){s[t].forEach(function(r){r.name in e&&null!=e[r.name]?a.push(n(t,r,e)):r.push in e&&null!=e[r.push]&&e[r.push].forEach(function(e){a.push(n(t,r,e))})})}),e.media.forEach(function(e){a.push(n("m",s.m[0],e)),i.forEach(function(t){s[t].forEach(function(r){r.name in e&&null!=e[r.name]?a.push(n(t,r,e)):r.push in e&&null!=e[r.push]&&e[r.push].forEach(function(e){a.push(n(t,r,e))})})})}),a.join("\r\n")+"\r\n"}},3953:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.EnhancedEventEmitter=void 0;const s=r(5528),i=new(r(2994).Logger)("EnhancedEventEmitter");class a extends s.EventEmitter{constructor(){super(),this.setMaxListeners(1/0)}close(){super.removeAllListeners()}emit(e,...t){return super.emit(e,...t)}safeEmit(e,...t){try{return super.emit(e,...t)}catch(t){i.error("safeEmit() | event listener threw an error [eventName:%s]:%o",e,t);try{super.emit("listenererror",e,t)}catch(e){}return Boolean(super.listenerCount(e))}}on(e,t){return super.on(e,t),this}off(e,t){return super.off(e,t),this}addListener(e,t){return super.on(e,t),this}prependListener(e,t){return super.prependListener(e,t),this}once(e,t){return super.once(e,t),this}prependOnceListener(e,t){return super.prependOnceListener(e,t),this}removeListener(e,t){return super.off(e,t),this}removeAllListeners(e){return super.removeAllListeners(e),this}listenerCount(e){return super.listenerCount(e)}listeners(e){return super.listeners(e)}rawListeners(e){return super.rawListeners(e)}}t.EnhancedEventEmitter=a},4253:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.AwaitQueueRemovedTaskError=t.AwaitQueueStoppedError=void 0;class r extends Error{constructor(e){super(e??"queue stopped"),this.name="AwaitQueueStoppedError","function"==typeof Error.captureStackTrace&&Error.captureStackTrace(this,r)}}t.AwaitQueueStoppedError=r;class s extends Error{constructor(e){super(e??"queue task removed"),this.name="AwaitQueueRemovedTaskError","function"==typeof Error.captureStackTrace&&Error.captureStackTrace(this,s)}}t.AwaitQueueRemovedTaskError=s},4256:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.addNackSupportForOpus=function(e){for(const t of e.codecs??[])"audio/opus"!==t.mimeType.toLowerCase()&&"audio/multiopus"!==t.mimeType.toLowerCase()||t.rtcpFeedback?.some(e=>"nack"===e.type&&!e.parameter)||(t.rtcpFeedback||(t.rtcpFeedback=[]),t.rtcpFeedback.push({type:"nack"}))}},4299:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.updateV7State=void 0;const s=r(2291),i=r(6011),a={};function n(e,t,r){return e.msecs??=-1/0,e.seq??=0,t>e.msecs?(e.seq=r[6]<<23|r[7]<<16|r[8]<<8|r[9],e.msecs=t):(e.seq=e.seq+1|0,0===e.seq&&e.msecs++),e}function o(e,t,r,s,i=0){if(e.length<16)throw new Error("Random bytes length must be >= 16");if(s){if(i<0||i+16>s.length)throw new RangeError(`UUID byte range ${i}:${i+15} is out of buffer bounds`)}else s=new Uint8Array(16),i=0;return t??=Date.now(),r??=127*e[6]<<24|e[7]<<16|e[8]<<8|e[9],s[i++]=t/1099511627776&255,s[i++]=t/4294967296&255,s[i++]=t/16777216&255,s[i++]=t/65536&255,s[i++]=t/256&255,s[i++]=255&t,s[i++]=112|r>>>28&15,s[i++]=r>>>20&255,s[i++]=128|r>>>14&63,s[i++]=r>>>6&255,s[i++]=r<<2&255|3&e[10],s[i++]=e[11],s[i++]=e[12],s[i++]=e[13],s[i++]=e[14],s[i++]=e[15],s}t.updateV7State=n,t.default=function(e,t,r){let c;if(e)c=o(e.random??e.rng?.()??(0,s.default)(),e.msecs,e.seq,t,r);else{const e=Date.now(),i=(0,s.default)();n(a,e,i),c=o(i,a.msecs,a.seq,t,r)}return t??(0,i.unsafeStringify)(c)}},4557:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.URL=t.DNS=void 0;const s=r(2829),i=r(2988);var a=r(2988);function n(e,t,r,a){return(0,i.default)(80,s.default,e,t,r,a)}Object.defineProperty(t,"DNS",{enumerable:!0,get:function(){return a.DNS}}),Object.defineProperty(t,"URL",{enumerable:!0,get:function(){return a.URL}}),n.DNS=i.DNS,n.URL=i.URL,t.default=n},4893:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.InvalidStateError=t.UnsupportedError=void 0;class r extends Error{constructor(e){super(e),this.name="UnsupportedError",Error.hasOwnProperty("captureStackTrace")?Error.captureStackTrace(this,r):this.stack=new Error(e).stack}}t.UnsupportedError=r;class s extends Error{constructor(e){super(e),this.name="InvalidStateError",Error.hasOwnProperty("captureStackTrace")?Error.captureStackTrace(this,s):this.stack=new Error(e).stack}}t.InvalidStateError=s},5020:(e,t,r)=>{var s=function(e){return String(Number(e))===e?Number(e):e},i=function(e,t,r){var i=e.name&&e.names;e.push&&!t[e.push]?t[e.push]=[]:i&&!t[e.name]&&(t[e.name]={});var a=e.push?{}:i?t[e.name]:t;!function(e,t,r,i){if(i&&!r)t[i]=s(e[1]);else for(var a=0;a<r.length;a+=1)null!=e[a+1]&&(t[r[a]]=s(e[a+1]))}(r.match(e.reg),a,e.names,e.name),e.push&&t[e.push].push(a)},a=r(5602),n=RegExp.prototype.test.bind(/^([a-z])=(.*)/);t.parse=function(e){var t={},r=[],s=t;return e.split(/(\r\n|\r|\n)/).filter(n).forEach(function(e){var t=e[0],n=e.slice(2);"m"===t&&(r.push({rtp:[],fmtp:[]}),s=r[r.length-1]);for(var o=0;o<(a[t]||[]).length;o+=1){var c=a[t][o];if(c.reg.test(n))return i(c,s,n)}}),t.media=r,t};var o=function(e,t){var r=t.split(/=(.+)/,2);return 2===r.length?e[r[0]]=s(r[1]):1===r.length&&t.length>1&&(e[r[0]]=void 0),e};t.parseParams=function(e){return e.split(/;\s?/).reduce(o,{})},t.parseFmtpConfig=t.parseParams,t.parsePayloads=function(e){return e.toString().split(" ").map(Number)},t.parseRemoteCandidates=function(e){for(var t=[],r=e.split(" ").map(s),i=0;i<r.length;i+=3)t.push({component:r[i],ip:r[i+1],port:r[i+2]});return t},t.parseImageAttributes=function(e){return e.split(" ").map(function(e){return e.substring(1,e.length-1).split(",").reduce(o,{})})},t.parseSimulcastStreamList=function(e){return e.split(";").map(function(e){return e.split(",").map(function(e){var t,r=!1;return"~"!==e[0]?t=s(e):(t=s(e.substring(1,e.length)),r=!0),{scid:t,paused:r}})})}},5248:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.generateRouterRtpCapabilities=function(){return s.deepFreeze({codecs:[{mimeType:"audio/opus",kind:"audio",preferredPayloadType:100,clockRate:48e3,channels:2,rtcpFeedback:[{type:"transport-cc"}],parameters:{useinbandfec:1,foo:"bar"}},{mimeType:"video/VP8",kind:"video",preferredPayloadType:101,clockRate:9e4,rtcpFeedback:[{type:"nack"},{type:"nack",parameter:"pli"},{type:"ccm",parameter:"fir"},{type:"goog-remb"},{type:"transport-cc"}],parameters:{"x-google-start-bitrate":1500}},{mimeType:"video/rtx",kind:"video",preferredPayloadType:102,clockRate:9e4,rtcpFeedback:[],parameters:{apt:101}},{mimeType:"video/H264",kind:"video",preferredPayloadType:103,clockRate:9e4,rtcpFeedback:[{type:"nack"},{type:"nack",parameter:"pli"},{type:"ccm",parameter:"fir"},{type:"goog-remb"},{type:"transport-cc"}],parameters:{"level-asymmetry-allowed":1,"packetization-mode":1,"profile-level-id":"42e01f"}},{mimeType:"video/rtx",kind:"video",preferredPayloadType:104,clockRate:9e4,rtcpFeedback:[],parameters:{apt:103}},{mimeType:"video/VP9",kind:"video",preferredPayloadType:105,clockRate:9e4,rtcpFeedback:[{type:"nack"},{type:"nack",parameter:"pli"},{type:"ccm",parameter:"fir"},{type:"goog-remb"},{type:"transport-cc"}],parameters:{"profile-id":0,"x-google-start-bitrate":1500}},{mimeType:"video/rtx",kind:"video",preferredPayloadType:106,clockRate:9e4,rtcpFeedback:[],parameters:{apt:105}}],headerExtensions:[{kind:"audio",uri:"urn:ietf:params:rtp-hdrext:sdes:mid",preferredId:1,preferredEncrypt:!1,direction:"sendrecv"},{kind:"video",uri:"urn:ietf:params:rtp-hdrext:sdes:mid",preferredId:1,preferredEncrypt:!1,direction:"sendrecv"},{kind:"video",uri:"urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id",preferredId:2,preferredEncrypt:!1,direction:"recvonly"},{kind:"video",uri:"urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id",preferredId:3,preferredEncrypt:!1,direction:"recvonly"},{kind:"audio",uri:"http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time",preferredId:4,preferredEncrypt:!1,direction:"sendrecv"},{kind:"video",uri:"http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time",preferredId:4,preferredEncrypt:!1,direction:"sendrecv"},{kind:"audio",uri:"http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01",preferredId:5,preferredEncrypt:!1,direction:"recvonly"},{kind:"video",uri:"http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01",preferredId:5,preferredEncrypt:!1,direction:"sendrecv"},{kind:"audio",uri:"urn:ietf:params:rtp-hdrext:ssrc-audio-level",preferredId:10,preferredEncrypt:!1,direction:"sendrecv"},{kind:"video",uri:"urn:3gpp:video-orientation",preferredId:11,preferredEncrypt:!1,direction:"sendrecv"},{kind:"video",uri:"urn:ietf:params:rtp-hdrext:toffset",preferredId:12,preferredEncrypt:!1,direction:"sendrecv"}]})},t.generateNativeRtpCapabilities=function(){return s.deepFreeze({codecs:[{mimeType:"audio/opus",kind:"audio",preferredPayloadType:111,clockRate:48e3,channels:2,rtcpFeedback:[{type:"transport-cc"}],parameters:{minptime:10,useinbandfec:1}},{mimeType:"audio/ISAC",kind:"audio",preferredPayloadType:103,clockRate:16e3,channels:1,rtcpFeedback:[{type:"transport-cc"}],parameters:{}},{mimeType:"audio/CN",kind:"audio",preferredPayloadType:106,clockRate:32e3,channels:1,rtcpFeedback:[{type:"transport-cc"}],parameters:{}},{mimeType:"audio/foo",kind:"audio",preferredPayloadType:107,clockRate:9e4,channels:4,rtcpFeedback:[{type:"foo-qwe-qwe"}],parameters:{foo:"lalala"}},{mimeType:"video/BAZCODEC",kind:"video",preferredPayloadType:100,clockRate:9e4,rtcpFeedback:[{type:"foo"},{type:"transport-cc"},{type:"ccm",parameter:"fir"},{type:"nack"},{type:"nack",parameter:"pli"}],parameters:{baz:"1234abcd"}},{mimeType:"video/rtx",kind:"video",preferredPayloadType:101,clockRate:9e4,rtcpFeedback:[],parameters:{apt:100}},{mimeType:"video/VP8",kind:"video",preferredPayloadType:96,clockRate:9e4,rtcpFeedback:[{type:"goog-remb"},{type:"transport-cc"},{type:"ccm",parameter:"fir"},{type:"nack"},{type:"nack",parameter:"pli"}],parameters:{baz:"1234abcd"}},{mimeType:"video/rtx",kind:"video",preferredPayloadType:97,clockRate:9e4,rtcpFeedback:[],parameters:{apt:96}},{mimeType:"video/VP9",kind:"video",preferredPayloadType:98,clockRate:9e4,rtcpFeedback:[{type:"goog-remb"},{type:"transport-cc"},{type:"ccm",parameter:"fir"},{type:"nack"},{type:"nack",parameter:"pli"}],parameters:{"profile-id":0}},{mimeType:"video/rtx",kind:"video",preferredPayloadType:99,clockRate:9e4,rtcpFeedback:[],parameters:{apt:98}}],headerExtensions:[{kind:"audio",uri:"urn:ietf:params:rtp-hdrext:sdes:mid",preferredId:1},{kind:"video",uri:"urn:ietf:params:rtp-hdrext:sdes:mid",preferredId:1},{kind:"video",uri:"urn:ietf:params:rtp-hdrext:toffset",preferredId:2},{kind:"video",uri:"http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time",preferredId:3},{kind:"video",uri:"urn:3gpp:video-orientation",preferredId:4},{kind:"video",uri:"http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01",preferredId:5},{kind:"video",uri:"http://www.webrtc.org/experiments/rtp-hdrext/playout-delay",preferredId:6},{kind:"video",uri:"http://www.webrtc.org/experiments/rtp-hdrext/video-content-type",preferredId:7},{kind:"video",uri:"http://www.webrtc.org/experiments/rtp-hdrext/video-timing",preferredId:8},{kind:"audio",uri:"urn:ietf:params:rtp-hdrext:ssrc-audio-level",preferredId:10}]})},t.generateNativeSctpCapabilities=function(){return s.deepFreeze({numStreams:{OS:2048,MIS:2048}})},t.generateLocalDtlsParameters=function(){return s.deepFreeze({fingerprints:[{algorithm:"sha-256",value:"82:5A:68:3D:36:C3:0A:DE:AF:E7:32:43:D2:88:83:57:AC:2D:65:E5:80:C4:B6:FB:AF:1A:A0:21:9F:6D:0C:AD"}],role:"auto"})},t.generateTransportRemoteParameters=function(){return{id:i(),iceParameters:s.deepFreeze({iceLite:!0,password:"yku5ej8nvfaor28lvtrabcx0wkrpkztz",usernameFragment:"h3hk1iz6qqlnqlne"}),iceCandidates:s.deepFreeze([{foundation:"udpcandidate",address:"*******",ip:"*******",port:40533,priority:1078862079,protocol:"udp",type:"host",tcpType:"passive"},{foundation:"udpcandidate",address:"*******",ip:"9:9:9:9:9:9",port:41333,priority:1078862089,protocol:"udp",type:"host",tcpType:"passive"}]),dtlsParameters:s.deepFreeze({fingerprints:[{algorithm:"sha-256",value:"A9:F4:E0:D2:74:D3:0F:D9:CA:A5:2F:9F:7F:47:FA:F0:C4:72:DD:73:49:D0:3B:14:90:20:51:30:1B:90:8E:71"},{algorithm:"sha-384",value:"03:D9:0B:87:13:98:F6:6D:BC:FC:92:2E:39:D4:E1:97:32:61:30:56:84:70:81:6E:D1:82:97:EA:D9:C1:21:0F:6B:C5:E7:7F:E1:97:0C:17:97:6E:CF:B3:EF:2E:74:B0"},{algorithm:"sha-512",value:"84:27:A4:28:A4:73:AF:43:02:2A:44:68:FF:2F:29:5C:3B:11:9A:60:F4:A8:F0:F5:AC:A0:E3:49:3E:B1:34:53:A9:85:CE:51:9B:ED:87:5E:B8:F4:8E:3D:FA:20:51:B8:96:EE:DA:56:DC:2F:5C:62:79:15:23:E0:21:82:2B:2C"}],role:"auto"}),sctpParameters:s.deepFreeze({port:5e3,OS:2048,MIS:2048,maxMessageSize:2e6})}},t.generateProducerRemoteParameters=function(){return s.deepFreeze({id:i()})},t.generateConsumerRemoteParameters=function({id:e,codecMimeType:t}={}){switch(t){case"audio/opus":return{id:e??i(),producerId:i(),kind:"audio",rtpParameters:s.deepFreeze({codecs:[{mimeType:"audio/opus",payloadType:100,clockRate:48e3,channels:2,rtcpFeedback:[{type:"transport-cc"}],parameters:{useinbandfec:1,foo:"bar"}}],encodings:[{ssrc:46687003}],headerExtensions:[{uri:"urn:ietf:params:rtp-hdrext:sdes:mid",id:1},{uri:"http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01",id:5},{uri:"urn:ietf:params:rtp-hdrext:ssrc-audio-level",id:10}],rtcp:{cname:"wB4Ql4lrsxYLjzuN",reducedSize:!0,mux:!0}})};case"audio/ISAC":return{id:e??i(),producerId:i(),kind:"audio",rtpParameters:s.deepFreeze({codecs:[{mimeType:"audio/ISAC",payloadType:111,clockRate:16e3,channels:1,rtcpFeedback:[{type:"transport-cc"}],parameters:{}}],encodings:[{ssrc:46687004}],headerExtensions:[{uri:"urn:ietf:params:rtp-hdrext:sdes:mid",id:1},{uri:"http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01",id:5}],rtcp:{cname:"wB4Ql4lrsxYLjzuN",reducedSize:!0,mux:!0}})};case"video/VP8":return{id:e??i(),producerId:i(),kind:"video",rtpParameters:s.deepFreeze({codecs:[{mimeType:"video/VP8",payloadType:101,clockRate:9e4,rtcpFeedback:[{type:"nack"},{type:"nack",parameter:"pli"},{type:"ccm",parameter:"fir"},{type:"goog-remb"},{type:"transport-cc"}],parameters:{"x-google-start-bitrate":1500}},{mimeType:"video/rtx",payloadType:102,clockRate:9e4,rtcpFeedback:[],parameters:{apt:101}}],encodings:[{ssrc:99991111,rtx:{ssrc:99991112}}],headerExtensions:[{uri:"urn:ietf:params:rtp-hdrext:sdes:mid",id:1},{uri:"http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time",id:4},{uri:"http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01",id:5},{uri:"urn:3gpp:video-orientation",id:11},{uri:"urn:ietf:params:rtp-hdrext:toffset",id:12}],rtcp:{cname:"wB4Ql4lrsxYLjzuN",reducedSize:!0,mux:!0}})};case"video/H264":return{id:e??i(),producerId:i(),kind:"video",rtpParameters:s.deepFreeze({codecs:[{mimeType:"video/H264",payloadType:103,clockRate:9e4,rtcpFeedback:[{type:"nack"},{type:"nack",parameter:"pli"},{type:"ccm",parameter:"fir"},{type:"goog-remb"},{type:"transport-cc"}],parameters:{"level-asymmetry-allowed":1,"packetization-mode":1,"profile-level-id":"42e01f"}},{mimeType:"video/rtx",payloadType:104,clockRate:9e4,rtcpFeedback:[],parameters:{apt:103}}],encodings:[{ssrc:99991113,rtx:{ssrc:99991114}}],headerExtensions:[{uri:"urn:ietf:params:rtp-hdrext:sdes:mid",id:1},{uri:"http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time",id:4},{uri:"http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01",id:5},{uri:"urn:3gpp:video-orientation",id:11},{uri:"urn:ietf:params:rtp-hdrext:toffset",id:12}],rtcp:{cname:"wB4Ql4lrsxYLjzuN",reducedSize:!0,mux:!0}})};default:throw new TypeError(`unknown codecMimeType '${t}'`)}},t.generateDataProducerRemoteParameters=function(){return s.deepFreeze({id:i()})},t.generateDataConsumerRemoteParameters=function({id:e}={}){return{id:e??i(),dataProducerId:i(),sctpStreamParameters:s.deepFreeze({streamId:666,maxPacketLifeTime:5e3,maxRetransmits:void 0})}};const s=r(1765);function i(){return String(s.generateRandomNumber())}},5328:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.FakeMediaStreamTrack=void 0;const s=r(182),i=r(5862),a=r(3785);class n extends i.FakeEventTarget{#e;#t;#r;#s;#i;#a;#n;#o;#c;#d;#p;#l=null;#h=null;#u=null;#m=null;#f=null;constructor({kind:e,id:t,label:r,contentHint:i,enabled:a,muted:n,readyState:o,capabilities:c,constraints:d,settings:p,data:l}){super(),this.#e=t??(0,s.v4)(),this.#t=e,this.#r=r??"",this.#n=i??"",this.#i=a??!0,this.#a=n??!1,this.#s=o??"live",this.#o=c??{},this.#c=d??{},this.#d=p??{},this.#p=l??{}}get id(){return this.#e}get kind(){return this.#t}get label(){return this.#r}get contentHint(){return this.#n}set contentHint(e){this.#n=e}get enabled(){return this.#i}set enabled(e){const t=this.#i!==e;this.#i=e,t&&this.dispatchEvent(new Event("enabledchange"))}get muted(){return this.#a}get readyState(){return this.#s}get data(){return this.#p}set data(e){this.#p=e}get onmute(){return this.#l}set onmute(e){this.#l&&this.removeEventListener("mute",this.#l),this.#l=e,e&&this.addEventListener("mute",e)}get onunmute(){return this.#h}set onunmute(e){this.#h&&this.removeEventListener("unmute",this.#h),this.#h=e,e&&this.addEventListener("unmute",e)}get onended(){return this.#u}set onended(e){this.#u&&this.removeEventListener("ended",this.#u),this.#u=e,e&&this.addEventListener("ended",e)}get onenabledchange(){return this.#m}set onenabledchange(e){this.#m&&this.removeEventListener("enabledchange",this.#m),this.#m=e,e&&this.addEventListener("enabledchange",e)}get onstopped(){return this.#f}set onstopped(e){this.#f&&this.removeEventListener("stopped",this.#f),this.#f=e,e&&this.addEventListener("stopped",e)}addEventListener(e,t,r){super.addEventListener(e,t,r)}removeEventListener(e,t){super.removeEventListener(e,t)}stop(){"ended"!==this.#s&&(this.#s="ended",this.dispatchEvent(new Event("stopped")))}clone({id:e,data:t}={}){return new n({id:e??(0,s.v4)(),kind:this.#t,label:this.#r,contentHint:this.#n,enabled:this.#i,muted:this.#a,readyState:this.#s,capabilities:(0,a.clone)(this.#o),constraints:(0,a.clone)(this.#c),settings:(0,a.clone)(this.#d),data:t??(0,a.clone)(this.#p)})}getCapabilities(){return this.#o}getConstraints(){return this.#c}async applyConstraints(e={}){return this.#c=e,Promise.resolve()}getSettings(){return this.#d}remoteStop(){"ended"!==this.#s&&(this.#s="ended",this.dispatchEvent(new Event("stopped")),this.dispatchEvent(new Event("ended")))}remoteMute(){this.#a||(this.#a=!0,this.dispatchEvent(new Event("mute")))}remoteUnmute(){this.#a&&(this.#a=!1,this.dispatchEvent(new Event("unmute")))}}t.FakeMediaStreamTrack=n},5528:e=>{"use strict";var t,r="object"==typeof Reflect?Reflect:null,s=r&&"function"==typeof r.apply?r.apply:function(e,t,r){return Function.prototype.apply.call(e,t,r)};t=r&&"function"==typeof r.ownKeys?r.ownKeys:Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:function(e){return Object.getOwnPropertyNames(e)};var i=Number.isNaN||function(e){return e!=e};function a(){a.init.call(this)}e.exports=a,e.exports.once=function(e,t){return new Promise(function(r,s){function i(r){e.removeListener(t,a),s(r)}function a(){"function"==typeof e.removeListener&&e.removeListener("error",i),r([].slice.call(arguments))}f(e,t,a,{once:!0}),"error"!==t&&function(e,t){"function"==typeof e.on&&f(e,"error",t,{once:!0})}(e,i)})},a.EventEmitter=a,a.prototype._events=void 0,a.prototype._eventsCount=0,a.prototype._maxListeners=void 0;var n=10;function o(e){if("function"!=typeof e)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof e)}function c(e){return void 0===e._maxListeners?a.defaultMaxListeners:e._maxListeners}function d(e,t,r,s){var i,a,n,d;if(o(r),void 0===(a=e._events)?(a=e._events=Object.create(null),e._eventsCount=0):(void 0!==a.newListener&&(e.emit("newListener",t,r.listener?r.listener:r),a=e._events),n=a[t]),void 0===n)n=a[t]=r,++e._eventsCount;else if("function"==typeof n?n=a[t]=s?[r,n]:[n,r]:s?n.unshift(r):n.push(r),(i=c(e))>0&&n.length>i&&!n.warned){n.warned=!0;var p=new Error("Possible EventEmitter memory leak detected. "+n.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit");p.name="MaxListenersExceededWarning",p.emitter=e,p.type=t,p.count=n.length,d=p,console&&console.warn&&console.warn(d)}return e}function p(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0===arguments.length?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function l(e,t,r){var s={fired:!1,wrapFn:void 0,target:e,type:t,listener:r},i=p.bind(s);return i.listener=r,s.wrapFn=i,i}function h(e,t,r){var s=e._events;if(void 0===s)return[];var i=s[t];return void 0===i?[]:"function"==typeof i?r?[i.listener||i]:[i]:r?function(e){for(var t=new Array(e.length),r=0;r<t.length;++r)t[r]=e[r].listener||e[r];return t}(i):m(i,i.length)}function u(e){var t=this._events;if(void 0!==t){var r=t[e];if("function"==typeof r)return 1;if(void 0!==r)return r.length}return 0}function m(e,t){for(var r=new Array(t),s=0;s<t;++s)r[s]=e[s];return r}function f(e,t,r,s){if("function"==typeof e.on)s.once?e.once(t,r):e.on(t,r);else{if("function"!=typeof e.addEventListener)throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof e);e.addEventListener(t,function i(a){s.once&&e.removeEventListener(t,i),r(a)})}}Object.defineProperty(a,"defaultMaxListeners",{enumerable:!0,get:function(){return n},set:function(e){if("number"!=typeof e||e<0||i(e))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");n=e}}),a.init=function(){void 0!==this._events&&this._events!==Object.getPrototypeOf(this)._events||(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},a.prototype.setMaxListeners=function(e){if("number"!=typeof e||e<0||i(e))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+e+".");return this._maxListeners=e,this},a.prototype.getMaxListeners=function(){return c(this)},a.prototype.emit=function(e){for(var t=[],r=1;r<arguments.length;r++)t.push(arguments[r]);var i="error"===e,a=this._events;if(void 0!==a)i=i&&void 0===a.error;else if(!i)return!1;if(i){var n;if(t.length>0&&(n=t[0]),n instanceof Error)throw n;var o=new Error("Unhandled error."+(n?" ("+n.message+")":""));throw o.context=n,o}var c=a[e];if(void 0===c)return!1;if("function"==typeof c)s(c,this,t);else{var d=c.length,p=m(c,d);for(r=0;r<d;++r)s(p[r],this,t)}return!0},a.prototype.addListener=function(e,t){return d(this,e,t,!1)},a.prototype.on=a.prototype.addListener,a.prototype.prependListener=function(e,t){return d(this,e,t,!0)},a.prototype.once=function(e,t){return o(t),this.on(e,l(this,e,t)),this},a.prototype.prependOnceListener=function(e,t){return o(t),this.prependListener(e,l(this,e,t)),this},a.prototype.removeListener=function(e,t){var r,s,i,a,n;if(o(t),void 0===(s=this._events))return this;if(void 0===(r=s[e]))return this;if(r===t||r.listener===t)0===--this._eventsCount?this._events=Object.create(null):(delete s[e],s.removeListener&&this.emit("removeListener",e,r.listener||t));else if("function"!=typeof r){for(i=-1,a=r.length-1;a>=0;a--)if(r[a]===t||r[a].listener===t){n=r[a].listener,i=a;break}if(i<0)return this;0===i?r.shift():function(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}(r,i),1===r.length&&(s[e]=r[0]),void 0!==s.removeListener&&this.emit("removeListener",e,n||t)}return this},a.prototype.off=a.prototype.removeListener,a.prototype.removeAllListeners=function(e){var t,r,s;if(void 0===(r=this._events))return this;if(void 0===r.removeListener)return 0===arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==r[e]&&(0===--this._eventsCount?this._events=Object.create(null):delete r[e]),this;if(0===arguments.length){var i,a=Object.keys(r);for(s=0;s<a.length;++s)"removeListener"!==(i=a[s])&&this.removeAllListeners(i);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if("function"==typeof(t=r[e]))this.removeListener(e,t);else if(void 0!==t)for(s=t.length-1;s>=0;s--)this.removeListener(e,t[s]);return this},a.prototype.listeners=function(e){return h(this,e,!0)},a.prototype.rawListeners=function(e){return h(this,e,!1)},a.listenerCount=function(e,t){return"function"==typeof e.listenerCount?e.listenerCount(t):u.call(e,t)},a.prototype.listenerCount=u,a.prototype.eventNames=function(){return this._eventsCount>0?t(this._events):[]}},5544:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.extractRtpCapabilities=function({sdpObject:e}){const t=new Map,r=new Map;for(const i of e.media){const e=i.type;switch(e){case"audio":case"video":break;default:continue}for(const r of i.rtp){const s={kind:e,mimeType:`${e}/${r.codec}`,preferredPayloadType:r.payload,clockRate:r.rate,channels:r.encoding,parameters:{},rtcpFeedback:[]};t.set(s.preferredPayloadType,s)}for(const e of i.fmtp??[]){const r=s.parseParams(e.config),i=t.get(e.payload);i&&(r?.hasOwnProperty("profile-level-id")&&(r["profile-level-id"]=String(r["profile-level-id"])),i.parameters=r)}for(const r of i.rtcpFb??[]){const s={type:r.type,parameter:r.subtype};if(s.parameter||delete s.parameter,"*"!==r.payload){const e=t.get(Number(r.payload));if(!e)continue;e.rtcpFeedback.push(s)}else for(const r of t.values())r.kind!==e||/.+\/rtx$/i.test(r.mimeType)||r.rtcpFeedback.push(s)}for(const t of i.ext??[]){if(t["encrypt-uri"])continue;const s={kind:e,uri:t.uri,preferredId:t.value};r.set(s.preferredId,s)}}return{codecs:Array.from(t.values()),headerExtensions:Array.from(r.values())}},t.extractDtlsParameters=function({sdpObject:e}){let t,r=e.setup,s=e.fingerprint;if(!r||!s){const t=(e.media??[]).find(e=>0!==e.port);t&&(r??=t.setup,s??=t.fingerprint)}if(!r)throw new Error("no a=setup found at SDP session or media level");if(!s)throw new Error("no a=fingerprint found at SDP session or media level");switch(r){case"active":t="client";break;case"passive":t="server";break;case"actpass":t="auto"}return{role:t,fingerprints:[{algorithm:s.type,value:s.hash}]}},t.getCname=function({offerMediaObject:e}){const t=(e.ssrcs??[]).find(e=>"cname"===e.attribute);return t?t.value:""},t.applyCodecParameters=function({offerRtpParameters:e,answerMediaObject:t}){for(const r of e.codecs){const e=r.mimeType.toLowerCase();if("audio/opus"!==e)continue;if(!(t.rtp??[]).find(e=>e.payload===r.payloadType))continue;t.fmtp=t.fmtp??[];let i=t.fmtp.find(e=>e.payload===r.payloadType);i||(i={payload:r.payloadType,config:""},t.fmtp.push(i));const a=s.parseParams(i.config);switch(e){case"audio/opus":{const e=r.parameters?.["sprop-stereo"];void 0!==e&&(a.stereo=Number(e)?1:0);break}}i.config="";for(const e of Object.keys(a))i.config&&(i.config+=";"),i.config+=`${e}=${a[e]}`}};const s=r(7363)},5601:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Transport=void 0;const s=r(8876),i=r(2994),a=r(3953),n=r(4893),o=r(1765),c=r(8046),d=r(9792),p=r(3518),l=r(7504),h=r(9166),u=new i.Logger("Transport");class m{consumerOptions;promise;resolve;reject;constructor(e){this.consumerOptions=e,this.promise=new Promise((e,t)=>{this.resolve=e,this.reject=t})}}class f extends a.EnhancedEventEmitter{_id;_closed=!1;_direction;_getSendExtendedRtpCapabilities;_recvRtpCapabilities;_canProduceByKind;_maxSctpMessageSize;_handler;_iceGatheringState="new";_connectionState="new";_appData;_producers=new Map;_consumers=new Map;_dataProducers=new Map;_dataConsumers=new Map;_probatorConsumerCreated=!1;_awaitQueue=new s.AwaitQueue;_pendingConsumerTasks=[];_consumerCreationInProgress=!1;_pendingPauseConsumers=new Map;_consumerPauseInProgress=!1;_pendingResumeConsumers=new Map;_consumerResumeInProgress=!1;_pendingCloseConsumers=new Map;_consumerCloseInProgress=!1;_observer=new a.EnhancedEventEmitter;constructor({direction:e,id:t,iceParameters:r,iceCandidates:s,dtlsParameters:i,sctpParameters:a,iceServers:n,iceTransportPolicy:c,additionalSettings:d,appData:p,handlerFactory:l,getSendExtendedRtpCapabilities:h,recvRtpCapabilities:m,canProduceByKind:f}){super(),u.debug("constructor() [id:%s, direction:%s]",t,e),this._id=t,this._direction=e,this._getSendExtendedRtpCapabilities=h,this._recvRtpCapabilities=m,this._canProduceByKind=f,this._maxSctpMessageSize=a?a.maxMessageSize:null;const g=o.clone(d)??{};delete g.iceServers,delete g.iceTransportPolicy,delete g.bundlePolicy,delete g.rtcpMuxPolicy,this._handler=l.factory({direction:e,iceParameters:r,iceCandidates:s,dtlsParameters:i,sctpParameters:a,iceServers:n,iceTransportPolicy:c,additionalSettings:g,getSendExtendedRtpCapabilities:this._getSendExtendedRtpCapabilities}),this._appData=p??{},this.handleHandler()}get id(){return this._id}get closed(){return this._closed}get direction(){return this._direction}get handler(){return this._handler}get iceGatheringState(){return this._iceGatheringState}get connectionState(){return this._connectionState}get appData(){return this._appData}set appData(e){this._appData=e}get observer(){return this._observer}close(){if(!this._closed){u.debug("close()"),this._closed=!0,this._awaitQueue.stop(),this._handler.close(),this._connectionState="closed";for(const e of this._producers.values())e.transportClosed();this._producers.clear();for(const e of this._consumers.values())e.transportClosed();this._consumers.clear();for(const e of this._dataProducers.values())e.transportClosed();this._dataProducers.clear();for(const e of this._dataConsumers.values())e.transportClosed();this._dataConsumers.clear(),this._observer.safeEmit("close"),super.close(),this._observer.close()}}async getStats(){if(this._closed)throw new n.InvalidStateError("closed");return this._handler.getTransportStats()}async restartIce({iceParameters:e}){if(u.debug("restartIce()"),this._closed)throw new n.InvalidStateError("closed");if(!e)throw new TypeError("missing iceParameters");return this._awaitQueue.push(async()=>await this._handler.restartIce(e),"transport.restartIce()")}async updateIceServers({iceServers:e}={}){if(u.debug("updateIceServers()"),this._closed)throw new n.InvalidStateError("closed");if(!Array.isArray(e))throw new TypeError("missing iceServers");return this._awaitQueue.push(async()=>this._handler.updateIceServers(e),"transport.updateIceServers()")}async produce({track:e,encodings:t,codecOptions:r,codec:s,stopTracks:i=!0,disableTrackOnPause:a=!0,zeroRtpOnPause:o=!1,onRtpSender:p,appData:l={}}={}){if(u.debug("produce() [track:%o]",e),this._closed)throw new n.InvalidStateError("closed");if(!e)throw new TypeError("missing track");if("send"!==this._direction)throw new n.UnsupportedError("not a sending Transport");if(!this._canProduceByKind[e.kind])throw new n.UnsupportedError(`cannot produce ${e.kind}`);if("ended"===e.readyState)throw new n.InvalidStateError("track ended");if(0===this.listenerCount("connect")&&"new"===this._connectionState)throw new TypeError('no "connect" listener set into this transport');if(0===this.listenerCount("produce"))throw new TypeError('no "produce" listener set into this transport');if(l&&"object"!=typeof l)throw new TypeError("if given, appData must be an object");return this._awaitQueue.push(async()=>{let n;if(t&&!Array.isArray(t))throw TypeError("encodings must be an array");t&&0===t.length?n=void 0:t&&(n=t.map(e=>{const t={active:!0};return!1===e.active&&(t.active=!1),"boolean"==typeof e.dtx&&(t.dtx=e.dtx),"string"==typeof e.scalabilityMode&&(t.scalabilityMode=e.scalabilityMode),"number"==typeof e.scaleResolutionDownBy&&(t.scaleResolutionDownBy=e.scaleResolutionDownBy),"number"==typeof e.maxBitrate&&(t.maxBitrate=e.maxBitrate),"number"==typeof e.maxFramerate&&(t.maxFramerate=e.maxFramerate),"boolean"==typeof e.adaptivePtime&&(t.adaptivePtime=e.adaptivePtime),"string"==typeof e.priority&&(t.priority=e.priority),"string"==typeof e.networkPriority&&(t.networkPriority=e.networkPriority),t}));const{localId:h,rtpParameters:u,rtpSender:m}=await this._handler.send({track:e,encodings:n,codecOptions:r,codec:s,onRtpSender:p});try{c.validateRtpParameters(u);const{id:t}=await new Promise((t,r)=>{this.safeEmit("produce",{kind:e.kind,rtpParameters:u,appData:l},t,r)}),r=new d.Producer({id:t,localId:h,rtpSender:m,track:e,rtpParameters:u,stopTracks:i,disableTrackOnPause:a,zeroRtpOnPause:o,appData:l});return this._producers.set(r.id,r),this.handleProducer(r),this._observer.safeEmit("newproducer",r),r}catch(e){throw this._handler.stopSending(h).catch(()=>{}),e}},"transport.produce()").catch(t=>{if(i)try{e.stop()}catch(e){}throw t})}async consume({id:e,producerId:t,kind:r,rtpParameters:s,streamId:i,onRtpReceiver:a,appData:d={}}){if(u.debug("consume()"),this._closed)throw new n.InvalidStateError("closed");if("recv"!==this._direction)throw new n.UnsupportedError("not a receiving Transport");if("string"!=typeof e)throw new TypeError("missing id");if("string"!=typeof t)throw new TypeError("missing producerId");if("audio"!==r&&"video"!==r)throw new TypeError(`invalid kind '${r}'`);if(0===this.listenerCount("connect")&&"new"===this._connectionState)throw new TypeError('no "connect" listener set into this transport');if(d&&"object"!=typeof d)throw new TypeError("if given, appData must be an object");const p=o.clone(s);if(!c.canReceive(p,this._recvRtpCapabilities))throw new n.UnsupportedError("cannot consume this Producer");const l=new m({id:e,producerId:t,kind:r,rtpParameters:p,streamId:i,onRtpReceiver:a,appData:d});return this._pendingConsumerTasks.push(l),queueMicrotask(()=>{this._closed||!1===this._consumerCreationInProgress&&this.createPendingConsumers()}),l.promise}async produceData({ordered:e=!0,maxPacketLifeTime:t,maxRetransmits:r,label:s="",protocol:i="",appData:a={}}={}){if(u.debug("produceData()"),this._closed)throw new n.InvalidStateError("closed");if("send"!==this._direction)throw new n.UnsupportedError("not a sending Transport");if(!this._maxSctpMessageSize)throw new n.UnsupportedError("SCTP not enabled by remote Transport");if(0===this.listenerCount("connect")&&"new"===this._connectionState)throw new TypeError('no "connect" listener set into this transport');if(0===this.listenerCount("producedata"))throw new TypeError('no "producedata" listener set into this transport');if(a&&"object"!=typeof a)throw new TypeError("if given, appData must be an object");return(t||r)&&(e=!1),this._awaitQueue.push(async()=>{const{dataChannel:n,sctpStreamParameters:o}=await this._handler.sendDataChannel({ordered:e,maxPacketLifeTime:t,maxRetransmits:r,label:s,protocol:i});c.validateSctpStreamParameters(o);const{id:d}=await new Promise((e,t)=>{this.safeEmit("producedata",{sctpStreamParameters:o,label:s,protocol:i,appData:a},e,t)}),p=new l.DataProducer({id:d,dataChannel:n,sctpStreamParameters:o,appData:a});return this._dataProducers.set(p.id,p),this.handleDataProducer(p),this._observer.safeEmit("newdataproducer",p),p},"transport.produceData()")}async consumeData({id:e,dataProducerId:t,sctpStreamParameters:r,label:s="",protocol:i="",appData:a={}}){if(u.debug("consumeData()"),this._closed)throw new n.InvalidStateError("closed");if("recv"!==this._direction)throw new n.UnsupportedError("not a receiving Transport");if(!this._maxSctpMessageSize)throw new n.UnsupportedError("SCTP not enabled by remote Transport");if("string"!=typeof e)throw new TypeError("missing id");if("string"!=typeof t)throw new TypeError("missing dataProducerId");if(0===this.listenerCount("connect")&&"new"===this._connectionState)throw new TypeError('no "connect" listener set into this transport');if(a&&"object"!=typeof a)throw new TypeError("if given, appData must be an object");const d=o.clone(r);return c.validateSctpStreamParameters(d),this._awaitQueue.push(async()=>{const{dataChannel:r}=await this._handler.receiveDataChannel({sctpStreamParameters:d,label:s,protocol:i}),n=new h.DataConsumer({id:e,dataProducerId:t,dataChannel:r,sctpStreamParameters:d,appData:a});return this._dataConsumers.set(n.id,n),this.handleDataConsumer(n),this._observer.safeEmit("newdataconsumer",n),n},"transport.consumeData()")}createPendingConsumers(){this._consumerCreationInProgress=!0,this._awaitQueue.push(async()=>{if(0===this._pendingConsumerTasks.length)return void u.debug("createPendingConsumers() | there is no Consumer to be created");const e=[...this._pendingConsumerTasks];let t;this._pendingConsumerTasks=[];const r=[];for(const t of e){const{id:e,kind:s,rtpParameters:i,streamId:a,onRtpReceiver:n}=t.consumerOptions;r.push({trackId:e,kind:s,rtpParameters:i,streamId:a,onRtpReceiver:n})}try{const s=await this._handler.receive(r);for(let r=0;r<s.length;++r){const i=e[r],a=s[r],{id:n,producerId:o,kind:c,rtpParameters:d,appData:l}=i.consumerOptions,{localId:h,rtpReceiver:u,track:m}=a,f=new p.Consumer({id:n,localId:h,producerId:o,rtpReceiver:u,track:m,rtpParameters:d,appData:l});this._consumers.set(f.id,f),this.handleConsumer(f),this._probatorConsumerCreated||t||"video"!==c||(t=f),this._observer.safeEmit("newconsumer",f),i.resolve(f)}}catch(t){for(const r of e)r.reject(t)}if(t)try{const e=c.generateProbatorRtpParameters(t.rtpParameters);await this._handler.receive([{trackId:"probator",kind:"video",rtpParameters:e}]),u.debug("createPendingConsumers() | Consumer for RTP probation created"),this._probatorConsumerCreated=!0}catch(e){u.error("createPendingConsumers() | failed to create Consumer for RTP probation:%o",e)}},"transport.createPendingConsumers()").then(()=>{this._consumerCreationInProgress=!1,this._pendingConsumerTasks.length>0&&this.createPendingConsumers()}).catch(()=>{})}pausePendingConsumers(){this._consumerPauseInProgress=!0,this._awaitQueue.push(async()=>{if(0===this._pendingPauseConsumers.size)return void u.debug("pausePendingConsumers() | there is no Consumer to be paused");const e=Array.from(this._pendingPauseConsumers.values());this._pendingPauseConsumers.clear();try{const t=e.map(e=>e.localId);await this._handler.pauseReceiving(t)}catch(e){u.error("pausePendingConsumers() | failed to pause Consumers:",e)}},"transport.pausePendingConsumers").then(()=>{this._consumerPauseInProgress=!1,this._pendingPauseConsumers.size>0&&this.pausePendingConsumers()}).catch(()=>{})}resumePendingConsumers(){this._consumerResumeInProgress=!0,this._awaitQueue.push(async()=>{if(0===this._pendingResumeConsumers.size)return void u.debug("resumePendingConsumers() | there is no Consumer to be resumed");const e=Array.from(this._pendingResumeConsumers.values());this._pendingResumeConsumers.clear();try{const t=e.map(e=>e.localId);await this._handler.resumeReceiving(t)}catch(e){u.error("resumePendingConsumers() | failed to resume Consumers:",e)}},"transport.resumePendingConsumers").then(()=>{this._consumerResumeInProgress=!1,this._pendingResumeConsumers.size>0&&this.resumePendingConsumers()}).catch(()=>{})}closePendingConsumers(){this._consumerCloseInProgress=!0,this._awaitQueue.push(async()=>{if(0===this._pendingCloseConsumers.size)return void u.debug("closePendingConsumers() | there is no Consumer to be closed");const e=Array.from(this._pendingCloseConsumers.values());this._pendingCloseConsumers.clear();try{await this._handler.stopReceiving(e.map(e=>e.localId))}catch(e){u.error("closePendingConsumers() | failed to close Consumers:",e)}},"transport.closePendingConsumers").then(()=>{this._consumerCloseInProgress=!1,this._pendingCloseConsumers.size>0&&this.closePendingConsumers()}).catch(()=>{})}handleHandler(){const e=this._handler;e.on("@connect",({dtlsParameters:e},t,r)=>{this._closed?r(new n.InvalidStateError("closed")):this.safeEmit("connect",{dtlsParameters:e},t,r)}),e.on("@icegatheringstatechange",e=>{e!==this._iceGatheringState&&(u.debug("ICE gathering state changed to %s",e),this._iceGatheringState=e,this._closed||this.safeEmit("icegatheringstatechange",e))}),e.on("@icecandidateerror",e=>{u.warn(`ICE candidate error [url:${e.url}, localAddress:${e.address}, localPort:${e.port}]: ${e.errorCode} "${e.errorText}"`),this.safeEmit("icecandidateerror",e)}),e.on("@connectionstatechange",e=>{e!==this._connectionState&&(u.debug("connection state changed to %s",e),this._connectionState=e,this._closed||this.safeEmit("connectionstatechange",e))})}handleProducer(e){e.on("@close",()=>{this._producers.delete(e.id),this._closed||this._awaitQueue.push(async()=>await this._handler.stopSending(e.localId),"producer @close event").catch(e=>u.warn("producer.close() failed:%o",e))}),e.on("@pause",(t,r)=>{this._awaitQueue.push(async()=>await this._handler.pauseSending(e.localId),"producer @pause event").then(t).catch(r)}),e.on("@resume",(t,r)=>{this._awaitQueue.push(async()=>await this._handler.resumeSending(e.localId),"producer @resume event").then(t).catch(r)}),e.on("@replacetrack",(t,r,s)=>{this._awaitQueue.push(async()=>await this._handler.replaceTrack(e.localId,t),"producer @replacetrack event").then(r).catch(s)}),e.on("@setmaxspatiallayer",(t,r,s)=>{this._awaitQueue.push(async()=>await this._handler.setMaxSpatialLayer(e.localId,t),"producer @setmaxspatiallayer event").then(r).catch(s)}),e.on("@setrtpencodingparameters",(t,r,s)=>{this._awaitQueue.push(async()=>await this._handler.setRtpEncodingParameters(e.localId,t),"producer @setrtpencodingparameters event").then(r).catch(s)}),e.on("@getstats",(t,r)=>{if(this._closed)return r(new n.InvalidStateError("closed"));this._handler.getSenderStats(e.localId).then(t).catch(r)})}handleConsumer(e){e.on("@close",()=>{this._consumers.delete(e.id),this._pendingPauseConsumers.delete(e.id),this._pendingResumeConsumers.delete(e.id),this._closed||(this._pendingCloseConsumers.set(e.id,e),!1===this._consumerCloseInProgress&&this.closePendingConsumers())}),e.on("@pause",()=>{this._pendingResumeConsumers.has(e.id)&&this._pendingResumeConsumers.delete(e.id),this._pendingPauseConsumers.set(e.id,e),queueMicrotask(()=>{this._closed||!1===this._consumerPauseInProgress&&this.pausePendingConsumers()})}),e.on("@resume",()=>{this._pendingPauseConsumers.has(e.id)&&this._pendingPauseConsumers.delete(e.id),this._pendingResumeConsumers.set(e.id,e),queueMicrotask(()=>{this._closed||!1===this._consumerResumeInProgress&&this.resumePendingConsumers()})}),e.on("@getstats",(t,r)=>{if(this._closed)return r(new n.InvalidStateError("closed"));this._handler.getReceiverStats(e.localId).then(t).catch(r)})}handleDataProducer(e){e.on("@close",()=>{this._dataProducers.delete(e.id)})}handleDataConsumer(e){e.on("@close",()=>{this._dataConsumers.delete(e.id)})}}t.Transport=f},5602:e=>{var t=e.exports={v:[{name:"version",reg:/^(\d*)$/}],o:[{name:"origin",reg:/^(\S*) (\d*) (\d*) (\S*) IP(\d) (\S*)/,names:["username","sessionId","sessionVersion","netType","ipVer","address"],format:"%s %s %d %s IP%d %s"}],s:[{name:"name"}],i:[{name:"description"}],u:[{name:"uri"}],e:[{name:"email"}],p:[{name:"phone"}],z:[{name:"timezones"}],r:[{name:"repeats"}],t:[{name:"timing",reg:/^(\d*) (\d*)/,names:["start","stop"],format:"%d %d"}],c:[{name:"connection",reg:/^IN IP(\d) (\S*)/,names:["version","ip"],format:"IN IP%d %s"}],b:[{push:"bandwidth",reg:/^(TIAS|AS|CT|RR|RS):(\d*)/,names:["type","limit"],format:"%s:%s"}],m:[{reg:/^(\w*) (\d*) ([\w/]*)(?: (.*))?/,names:["type","port","protocol","payloads"],format:"%s %d %s %s"}],a:[{push:"rtp",reg:/^rtpmap:(\d*) ([\w\-.]*)(?:\s*\/(\d*)(?:\s*\/(\S*))?)?/,names:["payload","codec","rate","encoding"],format:function(e){return e.encoding?"rtpmap:%d %s/%s/%s":e.rate?"rtpmap:%d %s/%s":"rtpmap:%d %s"}},{push:"fmtp",reg:/^fmtp:(\d*) ([\S| ]*)/,names:["payload","config"],format:"fmtp:%d %s"},{name:"control",reg:/^control:(.*)/,format:"control:%s"},{name:"rtcp",reg:/^rtcp:(\d*)(?: (\S*) IP(\d) (\S*))?/,names:["port","netType","ipVer","address"],format:function(e){return null!=e.address?"rtcp:%d %s IP%d %s":"rtcp:%d"}},{push:"rtcpFbTrrInt",reg:/^rtcp-fb:(\*|\d*) trr-int (\d*)/,names:["payload","value"],format:"rtcp-fb:%s trr-int %d"},{push:"rtcpFb",reg:/^rtcp-fb:(\*|\d*) ([\w-_]*)(?: ([\w-_]*))?/,names:["payload","type","subtype"],format:function(e){return null!=e.subtype?"rtcp-fb:%s %s %s":"rtcp-fb:%s %s"}},{push:"ext",reg:/^extmap:(\d+)(?:\/(\w+))?(?: (urn:ietf:params:rtp-hdrext:encrypt))? (\S*)(?: (\S*))?/,names:["value","direction","encrypt-uri","uri","config"],format:function(e){return"extmap:%d"+(e.direction?"/%s":"%v")+(e["encrypt-uri"]?" %s":"%v")+" %s"+(e.config?" %s":"")}},{name:"extmapAllowMixed",reg:/^(extmap-allow-mixed)/},{push:"crypto",reg:/^crypto:(\d*) ([\w_]*) (\S*)(?: (\S*))?/,names:["id","suite","config","sessionConfig"],format:function(e){return null!=e.sessionConfig?"crypto:%d %s %s %s":"crypto:%d %s %s"}},{name:"setup",reg:/^setup:(\w*)/,format:"setup:%s"},{name:"connectionType",reg:/^connection:(new|existing)/,format:"connection:%s"},{name:"mid",reg:/^mid:([^\s]*)/,format:"mid:%s"},{name:"msid",reg:/^msid:(.*)/,format:"msid:%s"},{name:"ptime",reg:/^ptime:(\d*(?:\.\d*)*)/,format:"ptime:%d"},{name:"maxptime",reg:/^maxptime:(\d*(?:\.\d*)*)/,format:"maxptime:%d"},{name:"direction",reg:/^(sendrecv|recvonly|sendonly|inactive)/},{name:"icelite",reg:/^(ice-lite)/},{name:"iceUfrag",reg:/^ice-ufrag:(\S*)/,format:"ice-ufrag:%s"},{name:"icePwd",reg:/^ice-pwd:(\S*)/,format:"ice-pwd:%s"},{name:"fingerprint",reg:/^fingerprint:(\S*) (\S*)/,names:["type","hash"],format:"fingerprint:%s %s"},{push:"candidates",reg:/^candidate:(\S*) (\d*) (\S*) (\d*) (\S*) (\d*) typ (\S*)(?: raddr (\S*) rport (\d*))?(?: tcptype (\S*))?(?: generation (\d*))?(?: network-id (\d*))?(?: network-cost (\d*))?/,names:["foundation","component","transport","priority","ip","port","type","raddr","rport","tcptype","generation","network-id","network-cost"],format:function(e){var t="candidate:%s %d %s %d %s %d typ %s";return t+=null!=e.raddr?" raddr %s rport %d":"%v%v",t+=null!=e.tcptype?" tcptype %s":"%v",null!=e.generation&&(t+=" generation %d"),(t+=null!=e["network-id"]?" network-id %d":"%v")+(null!=e["network-cost"]?" network-cost %d":"%v")}},{name:"endOfCandidates",reg:/^(end-of-candidates)/},{name:"remoteCandidates",reg:/^remote-candidates:(.*)/,format:"remote-candidates:%s"},{name:"iceOptions",reg:/^ice-options:(\S*)/,format:"ice-options:%s"},{push:"ssrcs",reg:/^ssrc:(\d*) ([\w_-]*)(?::(.*))?/,names:["id","attribute","value"],format:function(e){var t="ssrc:%d";return null!=e.attribute&&(t+=" %s",null!=e.value&&(t+=":%s")),t}},{push:"ssrcGroups",reg:/^ssrc-group:([\x21\x23\x24\x25\x26\x27\x2A\x2B\x2D\x2E\w]*) (.*)/,names:["semantics","ssrcs"],format:"ssrc-group:%s %s"},{name:"msidSemantic",reg:/^msid-semantic:\s?(\w*) (\S*)/,names:["semantic","token"],format:"msid-semantic: %s %s"},{push:"groups",reg:/^group:(\w*) (.*)/,names:["type","mids"],format:"group:%s %s"},{name:"rtcpMux",reg:/^(rtcp-mux)/},{name:"rtcpRsize",reg:/^(rtcp-rsize)/},{name:"sctpmap",reg:/^sctpmap:([\w_/]*) (\S*)(?: (\S*))?/,names:["sctpmapNumber","app","maxMessageSize"],format:function(e){return null!=e.maxMessageSize?"sctpmap:%s %s %s":"sctpmap:%s %s"}},{name:"xGoogleFlag",reg:/^x-google-flag:([^\s]*)/,format:"x-google-flag:%s"},{push:"rids",reg:/^rid:([\d\w]+) (\w+)(?: ([\S| ]*))?/,names:["id","direction","params"],format:function(e){return e.params?"rid:%s %s %s":"rid:%s %s"}},{push:"imageattrs",reg:new RegExp("^imageattr:(\\d+|\\*)[\\s\\t]+(send|recv)[\\s\\t]+(\\*|\\[\\S+\\](?:[\\s\\t]+\\[\\S+\\])*)(?:[\\s\\t]+(recv|send)[\\s\\t]+(\\*|\\[\\S+\\](?:[\\s\\t]+\\[\\S+\\])*))?"),names:["pt","dir1","attrs1","dir2","attrs2"],format:function(e){return"imageattr:%s %s %s"+(e.dir2?" %s %s":"")}},{name:"simulcast",reg:new RegExp("^simulcast:(send|recv) ([a-zA-Z0-9\\-_~;,]+)(?:\\s?(send|recv) ([a-zA-Z0-9\\-_~;,]+))?$"),names:["dir1","list1","dir2","list2"],format:function(e){return"simulcast:%s %s"+(e.dir2?" %s %s":"")}},{name:"simulcast_03",reg:/^simulcast:[\s\t]+([\S+\s\t]+)$/,names:["value"],format:"simulcast: %s"},{name:"framerate",reg:/^framerate:(\d+(?:$|\.\d+))/,format:"framerate:%s"},{name:"sourceFilter",reg:/^source-filter: *(excl|incl) (\S*) (IP4|IP6|\*) (\S*) (.*)/,names:["filterMode","netType","addressTypes","destAddress","srcList"],format:"source-filter: %s %s %s %s %s"},{name:"bundleOnly",reg:/^(bundle-only)/},{name:"label",reg:/^label:(.+)/,format:"label:%s"},{name:"sctpPort",reg:/^sctp-port:(\d+)$/,format:"sctp-port:%s"},{name:"maxMessageSize",reg:/^max-message-size:(\d+)$/,format:"max-message-size:%s"},{push:"tsRefClocks",reg:/^ts-refclk:([^\s=]*)(?:=(\S*))?/,names:["clksrc","clksrcExt"],format:function(e){return"ts-refclk:%s"+(null!=e.clksrcExt?"=%s":"")}},{name:"mediaClk",reg:/^mediaclk:(?:id=(\S*))? *([^\s=]*)(?:=(\S*))?(?: *rate=(\d+)\/(\d+))?/,names:["id","mediaClockName","mediaClockValue","rateNumerator","rateDenominator"],format:function(e){var t="mediaclk:";return t+=null!=e.id?"id=%s %s":"%v%s",t+=null!=e.mediaClockValue?"=%s":"",(t+=null!=e.rateNumerator?" rate=%s":"")+(null!=e.rateDenominator?"/%s":"")}},{name:"keywords",reg:/^keywds:(.+)$/,format:"keywds:%s"},{name:"content",reg:/^content:(.+)/,format:"content:%s"},{name:"bfcpFloorCtrl",reg:/^floorctrl:(c-only|s-only|c-s)/,format:"floorctrl:%s"},{name:"bfcpConfId",reg:/^confid:(\d+)/,format:"confid:%s"},{name:"bfcpUserId",reg:/^userid:(\d+)/,format:"userid:%s"},{name:"bfcpFloorId",reg:/^floorid:(.+) (?:m-stream|mstrm):(.+)/,names:["id","mStream"],format:"floorid:%s mstrm:%s"},{push:"invalid",names:["value"]}]};Object.keys(t).forEach(function(e){t[e].forEach(function(e){e.reg||(e.reg=/(.*)/),e.format||(e.format="%s")})})},5765:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Safari12=void 0;const s=r(7363),i=r(3953),a=r(2994),n=r(8046),o=r(4893),c=r(3303),d=r(1305),p=r(5544),l=r(5938),h=r(4256),u=new a.Logger("Safari12"),m="Safari12",f={OS:1024,MIS:1024};class g extends i.EnhancedEventEmitter{_closed=!1;_direction;_remoteSdp;_getSendExtendedRtpCapabilities;_forcedLocalDtlsRole;_pc;_mapMidTransceiver=new Map;_sendStream=new MediaStream;_hasDataChannelMediaSection=!1;_nextSendSctpStreamId=0;_transportReady=!1;static createFactory(){return{name:m,factory:e=>new g(e),getNativeRtpCapabilities:async()=>{u.debug("getNativeRtpCapabilities()");let e=new RTCPeerConnection({iceServers:[],iceTransportPolicy:"all",bundlePolicy:"max-bundle",rtcpMuxPolicy:"require"});try{e.addTransceiver("audio"),e.addTransceiver("video");const t=await e.createOffer();try{e.close()}catch(e){}e=void 0;const r=s.parse(t.sdp);return g.getLocalRtpCapabilities(r)}catch(t){try{e?.close()}catch(e){}throw e=void 0,t}},getNativeSctpCapabilities:async()=>(u.debug("getNativeSctpCapabilities()"),{numStreams:f})}}static getLocalRtpCapabilities(e){const t=p.extractRtpCapabilities({sdpObject:e});return h.addNackSupportForOpus(t),t}constructor({direction:e,iceParameters:t,iceCandidates:r,dtlsParameters:s,sctpParameters:i,iceServers:a,iceTransportPolicy:n,additionalSettings:o,getSendExtendedRtpCapabilities:c}){super(),u.debug("constructor()"),this._direction=e,this._remoteSdp=new d.RemoteSdp({iceParameters:t,iceCandidates:r,dtlsParameters:s,sctpParameters:i}),this._getSendExtendedRtpCapabilities=c,s.role&&"auto"!==s.role&&(this._forcedLocalDtlsRole="server"===s.role?"client":"server"),this._pc=new RTCPeerConnection({iceServers:a??[],iceTransportPolicy:n??"all",bundlePolicy:"max-bundle",rtcpMuxPolicy:"require",...o}),this._pc.addEventListener("icegatheringstatechange",()=>{this.emit("@icegatheringstatechange",this._pc.iceGatheringState)}),this._pc.addEventListener("icecandidateerror",e=>{this.emit("@icecandidateerror",e)}),this._pc.addEventListener("icegatheringstatechange",this.onIceGatheringStateChange),this._pc.addEventListener("icecandidateerror",this.onIceCandidateError),this._pc.connectionState?this._pc.addEventListener("connectionstatechange",this.onConnectionStateChange):(u.warn("run() | pc.connectionState not supported, using pc.iceConnectionState"),this._pc.addEventListener("iceconnectionstatechange",this.onIceConnectionStateChange))}get name(){return m}close(){if(u.debug("close()"),!this._closed){this._closed=!0;try{this._pc.close()}catch(e){}this._pc.removeEventListener("icegatheringstatechange",this.onIceGatheringStateChange),this._pc.removeEventListener("icecandidateerror",this.onIceCandidateError),this._pc.removeEventListener("connectionstatechange",this.onConnectionStateChange),this._pc.removeEventListener("iceconnectionstatechange",this.onIceConnectionStateChange),this.emit("@close"),super.close()}}async updateIceServers(e){this.assertNotClosed(),u.debug("updateIceServers()");const t=this._pc.getConfiguration();t.iceServers=e,this._pc.setConfiguration(t)}async restartIce(e){if(this.assertNotClosed(),u.debug("restartIce()"),this._remoteSdp.updateIceParameters(e),this._transportReady)if("send"===this._direction){const e=await this._pc.createOffer({iceRestart:!0});u.debug("restartIce() | calling pc.setLocalDescription() [offer:%o]",e),await this._pc.setLocalDescription(e);const t={type:"answer",sdp:this._remoteSdp.getSdp()};u.debug("restartIce() | calling pc.setRemoteDescription() [answer:%o]",t),await this._pc.setRemoteDescription(t)}else{const e={type:"offer",sdp:this._remoteSdp.getSdp()};u.debug("restartIce() | calling pc.setRemoteDescription() [offer:%o]",e),await this._pc.setRemoteDescription(e);const t=await this._pc.createAnswer();u.debug("restartIce() | calling pc.setLocalDescription() [answer:%o]",t),await this._pc.setLocalDescription(t)}}async getTransportStats(){return this.assertNotClosed(),this._pc.getStats()}async send({track:e,encodings:t,codecOptions:r,codec:i,onRtpSender:a}){this.assertNotClosed(),this.assertSendDirection(),u.debug("send() [kind:%s, track.id:%s]",e.kind,e.id);const o=this._remoteSdp.getNextMediaSectionIdx(),d=this._pc.addTransceiver(e,{direction:"sendonly",streams:[this._sendStream]});a&&a(d.sender);let h=await this._pc.createOffer(),m=s.parse(h.sdp);m.extmapAllowMixed&&this._remoteSdp.setSessionExtmapAllowMixed();const f=g.getLocalRtpCapabilities(m),_=this._getSendExtendedRtpCapabilities(f),b=n.getSendingRtpParameters(e.kind,_);b.codecs=n.reduceCodecs(b.codecs,i);const w=n.getSendingRemoteRtpParameters(e.kind,_);let v;w.codecs=n.reduceCodecs(w.codecs,i),this._transportReady||await this.setupTransport({localDtlsRole:this._forcedLocalDtlsRole??"client",localSdpObject:m});const y=(0,c.parse)((t??[{}])[0].scalabilityMode);t&&t.length>1&&(u.debug("send() | enabling legacy simulcast"),m=s.parse(h.sdp),v=m.media[o.idx],l.addLegacySimulcast({offerMediaObject:v,numStreams:t.length}),h={type:"offer",sdp:s.write(m)}),u.debug("send() | calling pc.setLocalDescription() [offer:%o]",h),await this._pc.setLocalDescription(h);const S=d.mid;if(b.mid=S,m=s.parse(this._pc.localDescription.sdp),v=m.media[o.idx],b.rtcp.cname=p.getCname({offerMediaObject:v}),b.encodings=l.getRtpEncodings({offerMediaObject:v}),t)for(let e=0;e<b.encodings.length;++e)t[e]&&Object.assign(b.encodings[e],t[e]);if(b.encodings.length>1&&("video/vp8"===b.codecs[0].mimeType.toLowerCase()||"video/h264"===b.codecs[0].mimeType.toLowerCase()))for(const e of b.encodings)e.scalabilityMode?e.scalabilityMode=`L1T${y.temporalLayers}`:e.scalabilityMode="L1T3";this._remoteSdp.send({offerMediaObject:v,reuseMid:o.reuseMid,offerRtpParameters:b,answerRtpParameters:w,codecOptions:r});const C={type:"answer",sdp:this._remoteSdp.getSdp()};return u.debug("send() | calling pc.setRemoteDescription() [answer:%o]",C),await this._pc.setRemoteDescription(C),this._mapMidTransceiver.set(S,d),{localId:S,rtpParameters:b,rtpSender:d.sender}}async stopSending(e){if(this.assertSendDirection(),this._closed)return;u.debug("stopSending() [localId:%s]",e);const t=this._mapMidTransceiver.get(e);if(!t)throw new Error("associated RTCRtpTransceiver not found");if(t.sender.replaceTrack(null),this._pc.removeTrack(t.sender),this._remoteSdp.closeMediaSection(t.mid))try{t.stop()}catch(e){}const r=await this._pc.createOffer();u.debug("stopSending() | calling pc.setLocalDescription() [offer:%o]",r),await this._pc.setLocalDescription(r);const s={type:"answer",sdp:this._remoteSdp.getSdp()};u.debug("stopSending() | calling pc.setRemoteDescription() [answer:%o]",s),await this._pc.setRemoteDescription(s),this._mapMidTransceiver.delete(e)}async pauseSending(e){this.assertNotClosed(),this.assertSendDirection(),u.debug("pauseSending() [localId:%s]",e);const t=this._mapMidTransceiver.get(e);if(!t)throw new Error("associated RTCRtpTransceiver not found");t.direction="inactive",this._remoteSdp.pauseMediaSection(e);const r=await this._pc.createOffer();u.debug("pauseSending() | calling pc.setLocalDescription() [offer:%o]",r),await this._pc.setLocalDescription(r);const s={type:"answer",sdp:this._remoteSdp.getSdp()};u.debug("pauseSending() | calling pc.setRemoteDescription() [answer:%o]",s),await this._pc.setRemoteDescription(s)}async resumeSending(e){this.assertNotClosed(),this.assertSendDirection(),u.debug("resumeSending() [localId:%s]",e);const t=this._mapMidTransceiver.get(e);if(!t)throw new Error("associated RTCRtpTransceiver not found");t.direction="sendonly",this._remoteSdp.resumeSendingMediaSection(e);const r=await this._pc.createOffer();u.debug("resumeSending() | calling pc.setLocalDescription() [offer:%o]",r),await this._pc.setLocalDescription(r);const s={type:"answer",sdp:this._remoteSdp.getSdp()};u.debug("resumeSending() | calling pc.setRemoteDescription() [answer:%o]",s),await this._pc.setRemoteDescription(s)}async replaceTrack(e,t){this.assertNotClosed(),this.assertSendDirection(),t?u.debug("replaceTrack() [localId:%s, track.id:%s]",e,t.id):u.debug("replaceTrack() [localId:%s, no track]",e);const r=this._mapMidTransceiver.get(e);if(!r)throw new Error("associated RTCRtpTransceiver not found");await r.sender.replaceTrack(t)}async setMaxSpatialLayer(e,t){this.assertNotClosed(),this.assertSendDirection(),u.debug("setMaxSpatialLayer() [localId:%s, spatialLayer:%s]",e,t);const r=this._mapMidTransceiver.get(e);if(!r)throw new Error("associated RTCRtpTransceiver not found");const s=r.sender.getParameters();s.encodings.forEach((e,r)=>{e.active=r<=t}),await r.sender.setParameters(s),this._remoteSdp.muxMediaSectionSimulcast(e,s.encodings);const i=await this._pc.createOffer();u.debug("setMaxSpatialLayer() | calling pc.setLocalDescription() [offer:%o]",i),await this._pc.setLocalDescription(i);const a={type:"answer",sdp:this._remoteSdp.getSdp()};u.debug("setMaxSpatialLayer() | calling pc.setRemoteDescription() [answer:%o]",a),await this._pc.setRemoteDescription(a)}async setRtpEncodingParameters(e,t){this.assertNotClosed(),this.assertSendDirection(),u.debug("setRtpEncodingParameters() [localId:%s, params:%o]",e,t);const r=this._mapMidTransceiver.get(e);if(!r)throw new Error("associated RTCRtpTransceiver not found");const s=r.sender.getParameters();s.encodings.forEach((e,r)=>{s.encodings[r]={...e,...t}}),await r.sender.setParameters(s),this._remoteSdp.muxMediaSectionSimulcast(e,s.encodings);const i=await this._pc.createOffer();u.debug("setRtpEncodingParameters() | calling pc.setLocalDescription() [offer:%o]",i),await this._pc.setLocalDescription(i);const a={type:"answer",sdp:this._remoteSdp.getSdp()};u.debug("setRtpEncodingParameters() | calling pc.setRemoteDescription() [answer:%o]",a),await this._pc.setRemoteDescription(a)}async getSenderStats(e){this.assertNotClosed(),this.assertSendDirection();const t=this._mapMidTransceiver.get(e);if(!t)throw new Error("associated RTCRtpTransceiver not found");return t.sender.getStats()}async sendDataChannel({ordered:e,maxPacketLifeTime:t,maxRetransmits:r,label:i,protocol:a}){this.assertNotClosed(),this.assertSendDirection();const n={negotiated:!0,id:this._nextSendSctpStreamId,ordered:e,maxPacketLifeTime:t,maxRetransmits:r,protocol:a};u.debug("sendDataChannel() [options:%o]",n);const o=this._pc.createDataChannel(i,n);if(this._nextSendSctpStreamId=++this._nextSendSctpStreamId%f.MIS,!this._hasDataChannelMediaSection){const e=await this._pc.createOffer(),t=s.parse(e.sdp),r=t.media.find(e=>"application"===e.type);this._transportReady||await this.setupTransport({localDtlsRole:this._forcedLocalDtlsRole??"client",localSdpObject:t}),u.debug("sendDataChannel() | calling pc.setLocalDescription() [offer:%o]",e),await this._pc.setLocalDescription(e),this._remoteSdp.sendSctpAssociation({offerMediaObject:r});const i={type:"answer",sdp:this._remoteSdp.getSdp()};u.debug("sendDataChannel() | calling pc.setRemoteDescription() [answer:%o]",i),await this._pc.setRemoteDescription(i),this._hasDataChannelMediaSection=!0}return{dataChannel:o,sctpStreamParameters:{streamId:n.id,ordered:n.ordered,maxPacketLifeTime:n.maxPacketLifeTime,maxRetransmits:n.maxRetransmits}}}async receive(e){this.assertNotClosed(),this.assertRecvDirection();const t=[],r=new Map;for(const t of e){const{trackId:e,kind:s,rtpParameters:i,streamId:a}=t;u.debug("receive() [trackId:%s, kind:%s]",e,s);const n=i.mid??String(this._mapMidTransceiver.size);r.set(e,n),this._remoteSdp.receive({mid:n,kind:s,offerRtpParameters:i,streamId:a??i.rtcp.cname,trackId:e})}const i={type:"offer",sdp:this._remoteSdp.getSdp()};u.debug("receive() | calling pc.setRemoteDescription() [offer:%o]",i),await this._pc.setRemoteDescription(i);for(const t of e){const{trackId:e,onRtpReceiver:s}=t;if(s){const t=r.get(e),i=this._pc.getTransceivers().find(e=>e.mid===t);if(!i)throw new Error("transceiver not found");s(i.receiver)}}let a=await this._pc.createAnswer();const n=s.parse(a.sdp);for(const t of e){const{trackId:e,rtpParameters:s}=t,i=r.get(e),a=n.media.find(e=>String(e.mid)===i);p.applyCodecParameters({offerRtpParameters:s,answerMediaObject:a})}a={type:"answer",sdp:s.write(n)},this._transportReady||await this.setupTransport({localDtlsRole:this._forcedLocalDtlsRole??"client",localSdpObject:n}),u.debug("receive() | calling pc.setLocalDescription() [answer:%o]",a),await this._pc.setLocalDescription(a);for(const s of e){const{trackId:e}=s,i=r.get(e),a=this._pc.getTransceivers().find(e=>e.mid===i);if(!a)throw new Error("new RTCRtpTransceiver not found");this._mapMidTransceiver.set(i,a),t.push({localId:i,track:a.receiver.track,rtpReceiver:a.receiver})}return t}async stopReceiving(e){if(this.assertRecvDirection(),this._closed)return;for(const t of e){u.debug("stopReceiving() [localId:%s]",t);const e=this._mapMidTransceiver.get(t);if(!e)throw new Error("associated RTCRtpTransceiver not found");this._remoteSdp.closeMediaSection(e.mid)}const t={type:"offer",sdp:this._remoteSdp.getSdp()};u.debug("stopReceiving() | calling pc.setRemoteDescription() [offer:%o]",t),await this._pc.setRemoteDescription(t);const r=await this._pc.createAnswer();u.debug("stopReceiving() | calling pc.setLocalDescription() [answer:%o]",r),await this._pc.setLocalDescription(r);for(const t of e)this._mapMidTransceiver.delete(t)}async pauseReceiving(e){this.assertNotClosed(),this.assertRecvDirection();for(const t of e){u.debug("pauseReceiving() [localId:%s]",t);const e=this._mapMidTransceiver.get(t);if(!e)throw new Error("associated RTCRtpTransceiver not found");e.direction="inactive",this._remoteSdp.pauseMediaSection(t)}const t={type:"offer",sdp:this._remoteSdp.getSdp()};u.debug("pauseReceiving() | calling pc.setRemoteDescription() [offer:%o]",t),await this._pc.setRemoteDescription(t);const r=await this._pc.createAnswer();u.debug("pauseReceiving() | calling pc.setLocalDescription() [answer:%o]",r),await this._pc.setLocalDescription(r)}async resumeReceiving(e){this.assertNotClosed(),this.assertRecvDirection();for(const t of e){u.debug("resumeReceiving() [localId:%s]",t);const e=this._mapMidTransceiver.get(t);if(!e)throw new Error("associated RTCRtpTransceiver not found");e.direction="recvonly",this._remoteSdp.resumeReceivingMediaSection(t)}const t={type:"offer",sdp:this._remoteSdp.getSdp()};u.debug("resumeReceiving() | calling pc.setRemoteDescription() [offer:%o]",t),await this._pc.setRemoteDescription(t);const r=await this._pc.createAnswer();u.debug("resumeReceiving() | calling pc.setLocalDescription() [answer:%o]",r),await this._pc.setLocalDescription(r)}async getReceiverStats(e){this.assertNotClosed(),this.assertRecvDirection();const t=this._mapMidTransceiver.get(e);if(!t)throw new Error("associated RTCRtpTransceiver not found");return t.receiver.getStats()}async receiveDataChannel({sctpStreamParameters:e,label:t,protocol:r}){this.assertNotClosed(),this.assertRecvDirection();const{streamId:i,ordered:a,maxPacketLifeTime:n,maxRetransmits:o}=e,c={negotiated:!0,id:i,ordered:a,maxPacketLifeTime:n,maxRetransmits:o,protocol:r};u.debug("receiveDataChannel() [options:%o]",c);const d=this._pc.createDataChannel(t,c);if(!this._hasDataChannelMediaSection){this._remoteSdp.receiveSctpAssociation();const e={type:"offer",sdp:this._remoteSdp.getSdp()};u.debug("receiveDataChannel() | calling pc.setRemoteDescription() [offer:%o]",e),await this._pc.setRemoteDescription(e);const t=await this._pc.createAnswer();if(!this._transportReady){const e=s.parse(t.sdp);await this.setupTransport({localDtlsRole:this._forcedLocalDtlsRole??"client",localSdpObject:e})}u.debug("receiveDataChannel() | calling pc.setRemoteDescription() [answer:%o]",t),await this._pc.setLocalDescription(t),this._hasDataChannelMediaSection=!0}return{dataChannel:d}}async setupTransport({localDtlsRole:e,localSdpObject:t}){t||(t=s.parse(this._pc.localDescription.sdp));const r=p.extractDtlsParameters({sdpObject:t});r.role=e,this._remoteSdp.updateDtlsRole("client"===e?"server":"client"),await new Promise((e,t)=>{this.safeEmit("@connect",{dtlsParameters:r},e,t)}),this._transportReady=!0}onIceGatheringStateChange=()=>{this.emit("@icegatheringstatechange",this._pc.iceGatheringState)};onIceCandidateError=e=>{this.emit("@icecandidateerror",e)};onConnectionStateChange=()=>{this.emit("@connectionstatechange",this._pc.connectionState)};onIceConnectionStateChange=()=>{switch(this._pc.iceConnectionState){case"checking":this.emit("@connectionstatechange","connecting");break;case"connected":case"completed":this.emit("@connectionstatechange","connected");break;case"failed":this.emit("@connectionstatechange","failed");break;case"disconnected":this.emit("@connectionstatechange","disconnected");break;case"closed":this.emit("@connectionstatechange","closed")}};assertNotClosed(){if(this._closed)throw new o.InvalidStateError("method called in a closed handler")}assertSendDirection(){if("send"!==this._direction)throw new Error('method can just be called for handlers with "send" direction')}assertRecvDirection(){if("recv"!==this._direction)throw new Error('method can just be called for handlers with "recv" direction')}}t.Safari12=g},5862:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.FakeEventTarget=void 0,t.FakeEventTarget=class{listeners={};addEventListener(e,t,r={}){t&&(this.listeners[e]??=[],this.listeners[e].push({callback:t,once:!0===r.once}))}removeEventListener(e,t){this.listeners[e]&&(this.listeners[e]=this.listeners[e].filter(e=>e.callback!==t))}dispatchEvent(e){if(!e||"string"!=typeof e.type)throw new Error("invalid event object");const t=this.listeners[e.type];if(!t)return!0;for(const r of[...t]){try{r.callback.call(this,e)}catch(e){setTimeout(()=>{throw e},0)}r.once&&this.removeEventListener(e.type,r.callback)}return!e.defaultPrevented}}},5938:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getRtpEncodings=function({offerMediaObject:e}){const t=new Set;for(const r of e.ssrcs??[]){const e=r.id;e&&t.add(e)}if(0===t.size)throw new Error("no a=ssrc lines found");const r=new Map;for(const s of e.ssrcGroups??[]){if("FID"!==s.semantics)continue;const e=s.ssrcs.split(/\s+/),i=Number(e[0]),a=Number(e[1]);t.has(i)&&(t.delete(i),t.delete(a),r.set(i,a))}for(const e of t)r.set(e,void 0);const s=[];for(const[e,t]of r){const r={ssrc:e};t&&(r.rtx={ssrc:t}),s.push(r)}return s},t.addLegacySimulcast=function({offerMediaObject:e,numStreams:t}){if(t<=1)throw new TypeError("numStreams must be greater than 1");const r=(e.ssrcs??[]).find(e=>"msid"===e.attribute);if(!r)throw new Error("a=ssrc line with msid information not found");const[s,i]=r.value.split(" "),a=Number(r.id);let n;(e.ssrcGroups??[]).some(e=>{if("FID"!==e.semantics)return!1;const t=e.ssrcs.split(/\s+/);return Number(t[0])===a&&(n=Number(t[1]),!0)});const o=(e.ssrcs??[]).find(e=>"cname"===e.attribute);if(!o)throw new Error("a=ssrc line with cname information not found");const c=o.value,d=[],p=[];for(let e=0;e<t;++e)d.push(a+e),n&&p.push(n+e);e.ssrcGroups=[],e.ssrcs=[],e.ssrcGroups.push({semantics:"SIM",ssrcs:d.join(" ")});for(const t of d)e.ssrcs.push({id:t,attribute:"cname",value:c}),e.ssrcs.push({id:t,attribute:"msid",value:`${s} ${i}`});for(let t=0;t<p.length;++t){const r=d[t],a=p[t];e.ssrcs.push({id:a,attribute:"cname",value:c}),e.ssrcs.push({id:a,attribute:"msid",value:`${s} ${i}`}),e.ssrcGroups.push({semantics:"FID",ssrcs:`${r} ${a}`})}}},6004:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Device=void 0,t.detectDeviceAsync=g,t.detectDevice=_;const s=r(2109),i=r(2994),a=r(3953),n=r(4893),o=r(1765),c=r(8046),d=r(5601),p=r(2183),l=r(11),h=r(2292),u=r(5765),m=r(867),f=new i.Logger("Device");async function g(e){return f.debug("detectDeviceAsync() [userAgent:%s]",e),e||"object"!=typeof navigator||(e=navigator.userAgent),w(await(0,s.UAParser)(e).withFeatureCheck())}function _(e){return f.debug("detectDevice() [userAgent:%s]",e),e||"object"!=typeof navigator||(e=navigator.userAgent),w((0,s.UAParser)(e))}class b{_handlerFactory;_handlerName;_loaded=!1;_getSendExtendedRtpCapabilities;_recvRtpCapabilities;_canProduceByKind={audio:!1,video:!1};_sctpCapabilities;_observer=new a.EnhancedEventEmitter;static async factory({handlerName:e,handlerFactory:t}={}){if(f.debug("factory()"),e&&t)throw new TypeError("just one of handlerName or handlerInterface can be given");if(!e&&!t&&!(e=await g()))throw new n.UnsupportedError("device not supported");return new b({handlerName:e,handlerFactory:t})}constructor({handlerName:e,handlerFactory:t}={}){if(f.debug("constructor()"),e&&t)throw new TypeError("just one of handlerName or handlerInterface can be given");if(t)this._handlerFactory=t;else{if(e)f.debug("constructor() | handler given: %s",e);else{if(!(e=_()))throw new n.UnsupportedError("device not supported");f.debug("constructor() | detected handler: %s",e)}switch(e){case"Chrome111":this._handlerFactory=p.Chrome111.createFactory();break;case"Chrome74":this._handlerFactory=l.Chrome74.createFactory();break;case"Firefox120":this._handlerFactory=h.Firefox120.createFactory();break;case"Safari12":this._handlerFactory=u.Safari12.createFactory();break;case"ReactNative106":this._handlerFactory=m.ReactNative106.createFactory();break;default:throw new TypeError(`unknown handlerName "${e}"`)}}this._handlerName=this._handlerFactory.name}get handlerName(){return this._handlerName}get loaded(){return this._loaded}get rtpCapabilities(){if(!this._loaded)throw new n.InvalidStateError("not loaded");return this._recvRtpCapabilities}get sctpCapabilities(){if(!this._loaded)throw new n.InvalidStateError("not loaded");return this._sctpCapabilities}get observer(){return this._observer}async load({routerRtpCapabilities:e,preferLocalCodecsOrder:t=!1}){if(f.debug("load() [routerRtpCapabilities:%o]",e),this._loaded)throw new n.InvalidStateError("already loaded");const r=o.clone(e);c.validateRtpCapabilities(r);const{getNativeRtpCapabilities:s,getNativeSctpCapabilities:i}=this._handlerFactory,a=o.clone(await s());c.validateRtpCapabilities(a),f.debug("load() | got native RTP capabilities:%o",a),this._getSendExtendedRtpCapabilities=e=>o.clone(c.getExtendedRtpCapabilities(e,r,t));const d=c.getExtendedRtpCapabilities(a,r,!1);this._recvRtpCapabilities=c.getRecvRtpCapabilities(d),c.validateRtpCapabilities(this._recvRtpCapabilities),f.debug("load() | got receiving RTP capabilities:%o",this._recvRtpCapabilities),this._canProduceByKind.audio=c.canSend("audio",this._recvRtpCapabilities),this._canProduceByKind.video=c.canSend("video",this._recvRtpCapabilities),this._sctpCapabilities=await i(),c.validateSctpCapabilities(this._sctpCapabilities),f.debug("load() | got native SCTP capabilities:%o",this._sctpCapabilities),f.debug("load() succeeded"),this._loaded=!0}canProduce(e){if(!this._loaded)throw new n.InvalidStateError("not loaded");if("audio"!==e&&"video"!==e)throw new TypeError(`invalid kind "${e}"`);return this._canProduceByKind[e]}createSendTransport({id:e,iceParameters:t,iceCandidates:r,dtlsParameters:s,sctpParameters:i,iceServers:a,iceTransportPolicy:n,additionalSettings:o,appData:c}){return f.debug("createSendTransport()"),this.createTransport({direction:"send",id:e,iceParameters:t,iceCandidates:r,dtlsParameters:s,sctpParameters:i,iceServers:a,iceTransportPolicy:n,additionalSettings:o,appData:c})}createRecvTransport({id:e,iceParameters:t,iceCandidates:r,dtlsParameters:s,sctpParameters:i,iceServers:a,iceTransportPolicy:n,additionalSettings:o,appData:c}){return f.debug("createRecvTransport()"),this.createTransport({direction:"recv",id:e,iceParameters:t,iceCandidates:r,dtlsParameters:s,sctpParameters:i,iceServers:a,iceTransportPolicy:n,additionalSettings:o,appData:c})}createTransport({direction:e,id:t,iceParameters:r,iceCandidates:s,dtlsParameters:i,sctpParameters:a,iceServers:o,iceTransportPolicy:c,additionalSettings:p,appData:l}){if(!this._loaded)throw new n.InvalidStateError("not loaded");if("string"!=typeof t)throw new TypeError("missing id");if("object"!=typeof r)throw new TypeError("missing iceParameters");if(!Array.isArray(s))throw new TypeError("missing iceCandidates");if("object"!=typeof i)throw new TypeError("missing dtlsParameters");if(a&&"object"!=typeof a)throw new TypeError("wrong sctpParameters");if(l&&"object"!=typeof l)throw new TypeError("if given, appData must be an object");const h=new d.Transport({direction:e,id:t,iceParameters:r,iceCandidates:s,dtlsParameters:i,sctpParameters:a,iceServers:o,iceTransportPolicy:c,additionalSettings:p,appData:l,handlerFactory:this._handlerFactory,getSendExtendedRtpCapabilities:this._getSendExtendedRtpCapabilities,recvRtpCapabilities:this._recvRtpCapabilities,canProduceByKind:this._canProduceByKind});return this._observer.safeEmit("newtransport",h),h}}function w(e){if("object"==typeof navigator&&"ReactNative"===navigator.product)return f.debug("detectDeviceImpl() | React-Native detected"),"undefined"==typeof RTCPeerConnection||"undefined"==typeof RTCRtpTransceiver?void f.warn("detectDeviceImpl() | unsupported react-native-webrtc without RTCPeerConnection or RTCRtpTransceiver, forgot to call registerGlobals() on it?"):"ReactNative106";{f.debug("detectDeviceImpl() | browser detected [userAgent:%s, parsed:%o]",e.ua,e);const t=e.browser,r=t.name?.toLowerCase(),s=parseInt(t.major??"0"),i=e.engine,a=i.name?.toLowerCase(),n=e.os,o=n.name?.toLowerCase(),c=parseFloat(n.version??"0"),d=e.device,p=d.model?.toLowerCase(),l="ios"===o||"ipad"===p,h=r&&["chrome","chromium","mobile chrome","chrome webview","chrome headless"].includes(r),u=r&&["firefox","mobile firefox","mobile focus"].includes(r),m=r&&["safari","mobile safari"].includes(r),g=r&&["edge"].includes(r);if((h||g)&&!l&&s>=111)return"Chrome111";if(h&&!l&&s>=74||g&&!l&&s>=88)return"Chrome74";if(u&&!l&&s>=120)return"Firefox120";if(u&&l&&c>=14.3)return"Safari12";if(m&&s>=12&&"undefined"!=typeof RTCRtpTransceiver&&RTCRtpTransceiver.prototype.hasOwnProperty("currentDirection"))return"Safari12";if("webkit"===a&&l&&"undefined"!=typeof RTCRtpTransceiver&&RTCRtpTransceiver.prototype.hasOwnProperty("currentDirection"))return"Safari12";if("blink"===a){const t=e.ua.match(/(?:(?:Chrome|Chromium))[ /](\w+)/i);return t?Number(t[1])>=111?"Chrome111":"Chrome74":"Chrome111"}return void f.warn("detectDeviceImpl() | browser not supported [name:%s, version:%s]",r,s)}}t.Device=b},6011:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.unsafeStringify=void 0;const s=r(9746),i=[];for(let e=0;e<256;++e)i.push((e+256).toString(16).slice(1));function a(e,t=0){return(i[e[t+0]]+i[e[t+1]]+i[e[t+2]]+i[e[t+3]]+"-"+i[e[t+4]]+i[e[t+5]]+"-"+i[e[t+6]]+i[e[t+7]]+"-"+i[e[t+8]]+i[e[t+9]]+"-"+i[e[t+10]]+i[e[t+11]]+i[e[t+12]]+i[e[t+13]]+i[e[t+14]]+i[e[t+15]]).toLowerCase()}t.unsafeStringify=a,t.default=function(e,t=0){const r=a(e,t);if(!(0,s.default)(r))throw TypeError("Stringified UUID is invalid");return r}},6356:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const s=r(6011),i=r(1425),a=r(6568);t.default=function(e,t,r){e??={},r??=0;let n=(0,i.default)({...e,_v6:!0},new Uint8Array(16));if(n=(0,a.default)(n),t){for(let e=0;e<16;e++)t[r+e]=n[e];return t}return(0,s.unsafeStringify)(n)}},6568:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const s=r(1797),i=r(6011);t.default=function(e){const t=(r="string"==typeof e?(0,s.default)(e):e,Uint8Array.of((15&r[6])<<4|r[7]>>4&15,(15&r[7])<<4|(240&r[4])>>4,(15&r[4])<<4|(240&r[5])>>4,(15&r[5])<<4|(240&r[0])>>4,(15&r[0])<<4|(240&r[1])>>4,(15&r[1])<<4|(240&r[2])>>4,96|15&r[2],r[3],r[8],r[9],r[10],r[11],r[12],r[13],r[14],r[15]));var r;return"string"==typeof e?(0,i.unsafeStringify)(t):t}},6585:e=>{var t=1e3,r=60*t,s=60*r,i=24*s,a=7*i;function n(e,t,r,s){var i=t>=1.5*r;return Math.round(e/r)+" "+s+(i?"s":"")}e.exports=function(e,o){o=o||{};var c,d,p=typeof e;if("string"===p&&e.length>0)return function(e){if(!((e=String(e)).length>100)){var n=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(n){var o=parseFloat(n[1]);switch((n[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*o;case"weeks":case"week":case"w":return o*a;case"days":case"day":case"d":return o*i;case"hours":case"hour":case"hrs":case"hr":case"h":return o*s;case"minutes":case"minute":case"mins":case"min":case"m":return o*r;case"seconds":case"second":case"secs":case"sec":case"s":return o*t;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return o;default:return}}}}(e);if("number"===p&&isFinite(e))return o.long?(c=e,(d=Math.abs(c))>=i?n(c,d,i,"day"):d>=s?n(c,d,s,"hour"):d>=r?n(c,d,r,"minute"):d>=t?n(c,d,t,"second"):c+" ms"):function(e){var a=Math.abs(e);return a>=i?Math.round(e/i)+"d":a>=s?Math.round(e/s)+"h":a>=r?Math.round(e/r)+"m":a>=t?Math.round(e/t)+"s":e+"ms"}(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},6697:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-8][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000|ffffffff-ffff-ffff-ffff-ffffffffffff)$/i},7363:(e,t,r)=>{var s=r(5020),i=r(3804),a=r(5602);t.grammar=a,t.write=i,t.parse=s.parse,t.parseParams=s.parseParams,t.parseFmtpConfig=s.parseFmtpConfig,t.parsePayloads=s.parsePayloads,t.parseRemoteCandidates=s.parseRemoteCandidates,t.parseImageAttributes=s.parseImageAttributes,t.parseSimulcastStreamList=s.parseSimulcastStreamList},7504:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DataProducer=void 0;const s=r(2994),i=r(3953),a=r(4893),n=new s.Logger("DataProducer");class o extends i.EnhancedEventEmitter{_id;_dataChannel;_closed=!1;_sctpStreamParameters;_appData;_observer=new i.EnhancedEventEmitter;constructor({id:e,dataChannel:t,sctpStreamParameters:r,appData:s}){super(),n.debug("constructor()"),this._id=e,this._dataChannel=t,this._sctpStreamParameters=r,this._appData=s??{},this.handleDataChannel()}get id(){return this._id}get closed(){return this._closed}get sctpStreamParameters(){return this._sctpStreamParameters}get readyState(){return this._dataChannel.readyState}get label(){return this._dataChannel.label}get protocol(){return this._dataChannel.protocol}get bufferedAmount(){return this._dataChannel.bufferedAmount}get bufferedAmountLowThreshold(){return this._dataChannel.bufferedAmountLowThreshold}set bufferedAmountLowThreshold(e){this._dataChannel.bufferedAmountLowThreshold=e}get appData(){return this._appData}set appData(e){this._appData=e}get observer(){return this._observer}close(){this._closed||(n.debug("close()"),this._closed=!0,this._dataChannel.close(),this.emit("@close"),this._observer.safeEmit("close"),super.close(),this._observer.close())}transportClosed(){this._closed||(n.debug("transportClosed()"),this._closed=!0,this._dataChannel.close(),this.safeEmit("transportclose"),this._observer.safeEmit("close"))}send(e){if(n.debug("send()"),this._closed)throw new a.InvalidStateError("closed");this._dataChannel.send(e)}handleDataChannel(){this._dataChannel.addEventListener("open",()=>{this._closed||(n.debug('DataChannel "open" event'),this.safeEmit("open"))}),this._dataChannel.addEventListener("error",e=>{if(this._closed)return;const t=e.error??new Error("unknown DataChannel error");"sctp-failure"===e.error?.errorDetail?n.error("DataChannel SCTP error [sctpCauseCode:%s]: %s",e.error?.sctpCauseCode,e.error.message):n.error('DataChannel "error" event: %o',t),this.safeEmit("error",t)}),this._dataChannel.addEventListener("close",()=>{this._closed||(n.warn('DataChannel "close" event'),this._closed=!0,this.emit("@close"),this.safeEmit("close"),this._observer.safeEmit("close"))}),this._dataChannel.addEventListener("message",()=>{this._closed||n.warn('DataChannel "message" event in a DataProducer, message discarded')}),this._dataChannel.addEventListener("bufferedamountlow",()=>{this._closed||this.safeEmit("bufferedamountlow")})}}t.DataProducer=o},7833:(e,t,r)=>{t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;const r="color: "+this.color;t.splice(1,0,r,"color: inherit");let s=0,i=0;t[0].replace(/%[a-zA-Z%]/g,e=>{"%%"!==e&&(s++,"%c"===e&&(i=s))}),t.splice(i,0,r)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")||t.storage.getItem("DEBUG")}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e},t.useColors=function(){if("undefined"!=typeof window&&window.process&&("renderer"===window.process.type||window.process.__nwjs))return!0;if("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let e;return"undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=r(736)(t);const{formatters:s}=e.exports;s.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},8046:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.validateRtpCapabilities=function(e){if("object"!=typeof e)throw new TypeError("caps is not an object");if(e.codecs&&!Array.isArray(e.codecs))throw new TypeError("caps.codecs is not an array");e.codecs||(e.codecs=[]);for(const t of e.codecs)d(t);if(e.headerExtensions&&!Array.isArray(e.headerExtensions))throw new TypeError("caps.headerExtensions is not an array");e.headerExtensions||(e.headerExtensions=[]);for(const t of e.headerExtensions)l(t)},t.validateRtpParameters=c,t.validateSctpStreamParameters=function(e){if("object"!=typeof e)throw new TypeError("params is not an object");if("number"!=typeof e.streamId)throw new TypeError("missing params.streamId");let t=!1;if("boolean"==typeof e.ordered?t=!0:e.ordered=!0,e.maxPacketLifeTime&&"number"!=typeof e.maxPacketLifeTime)throw new TypeError("invalid params.maxPacketLifeTime");if(e.maxRetransmits&&"number"!=typeof e.maxRetransmits)throw new TypeError("invalid params.maxRetransmits");if(e.maxPacketLifeTime&&e.maxRetransmits)throw new TypeError("cannot provide both maxPacketLifeTime and maxRetransmits");if(t&&e.ordered&&(e.maxPacketLifeTime||e.maxRetransmits))throw new TypeError("cannot be ordered with maxPacketLifeTime or maxRetransmits");if(t||!e.maxPacketLifeTime&&!e.maxRetransmits||(e.ordered=!1),e.label&&"string"!=typeof e.label)throw new TypeError("invalid params.label");if(e.protocol&&"string"!=typeof e.protocol)throw new TypeError("invalid params.protocol")},t.validateSctpCapabilities=function(e){if("object"!=typeof e)throw new TypeError("caps is not an object");if(!e.numStreams||"object"!=typeof e.numStreams)throw new TypeError("missing caps.numStreams");!function(e){if("object"!=typeof e)throw new TypeError("numStreams is not an object");if("number"!=typeof e.OS)throw new TypeError("missing numStreams.OS");if("number"!=typeof e.MIS)throw new TypeError("missing numStreams.MIS")}(e.numStreams)},t.getExtendedRtpCapabilities=function(e,t,r){const s={codecs:[],headerExtensions:[]};if(r)for(const r of e.codecs??[]){if(f(r))continue;const e=(t.codecs??[]).find(e=>g(e,r,{strict:!0,modify:!0}));if(!e)continue;const i={kind:r.kind,mimeType:r.mimeType,clockRate:r.clockRate,channels:r.channels,localPayloadType:r.preferredPayloadType,localRtxPayloadType:void 0,remotePayloadType:e.preferredPayloadType,remoteRtxPayloadType:void 0,localParameters:r.parameters??{},remoteParameters:e.parameters??{},rtcpFeedback:_(r,e)};s.codecs.push(i)}else for(const r of t.codecs??[]){if(f(r))continue;const t=(e.codecs??[]).find(e=>g(e,r,{strict:!0,modify:!0}));if(!t)continue;const i={kind:t.kind,mimeType:t.mimeType,clockRate:t.clockRate,channels:t.channels,localPayloadType:t.preferredPayloadType,localRtxPayloadType:void 0,remotePayloadType:r.preferredPayloadType,remoteRtxPayloadType:void 0,localParameters:t.parameters??{},remoteParameters:r.parameters??{},rtcpFeedback:_(t,r)};s.codecs.push(i)}for(const r of s.codecs){const s=e.codecs.find(e=>f(e)&&e.parameters?.apt===r.localPayloadType),i=t.codecs.find(e=>f(e)&&e.parameters?.apt===r.remotePayloadType);s&&i&&(r.localRtxPayloadType=s.preferredPayloadType,r.remoteRtxPayloadType=i.preferredPayloadType)}for(const r of t.headerExtensions){const t=e.headerExtensions.find(e=>{return s=r,!((t=e).kind&&s.kind&&t.kind!==s.kind||t.uri!==s.uri);var t,s});if(!t)continue;const i={kind:r.kind,uri:r.uri,sendId:t.preferredId,recvId:r.preferredId,encrypt:t.preferredEncrypt??!1,direction:"sendrecv"};switch(r.direction){case"sendrecv":i.direction="sendrecv";break;case"recvonly":i.direction="sendonly";break;case"sendonly":i.direction="recvonly";break;case"inactive":i.direction="inactive"}s.headerExtensions.push(i)}return s},t.getRecvRtpCapabilities=function(e){const t={codecs:[],headerExtensions:[]};for(const r of e.codecs){const e={kind:r.kind,mimeType:r.mimeType,preferredPayloadType:r.remotePayloadType,clockRate:r.clockRate,channels:r.channels,parameters:r.localParameters,rtcpFeedback:r.rtcpFeedback};if(t.codecs.push(e),!r.remoteRtxPayloadType)continue;const s={kind:r.kind,mimeType:`${r.kind}/rtx`,preferredPayloadType:r.remoteRtxPayloadType,clockRate:r.clockRate,parameters:{apt:r.remotePayloadType},rtcpFeedback:[]};t.codecs.push(s)}for(const r of e.headerExtensions){if("sendrecv"!==r.direction&&"recvonly"!==r.direction)continue;const e={kind:r.kind,uri:r.uri,preferredId:r.recvId,preferredEncrypt:r.encrypt??!1,direction:r.direction};t.headerExtensions.push(e)}return t},t.getSendingRtpParameters=function(e,t){const r={mid:void 0,codecs:[],headerExtensions:[],encodings:[],rtcp:{}};for(const s of t.codecs){if(s.kind!==e)continue;const t={mimeType:s.mimeType,payloadType:s.localPayloadType,clockRate:s.clockRate,channels:s.channels,parameters:s.localParameters,rtcpFeedback:s.rtcpFeedback};if(r.codecs.push(t),s.localRtxPayloadType){const e={mimeType:`${s.kind}/rtx`,payloadType:s.localRtxPayloadType,clockRate:s.clockRate,parameters:{apt:s.localPayloadType},rtcpFeedback:[]};r.codecs.push(e)}}for(const s of t.headerExtensions){if(s.kind&&s.kind!==e||"sendrecv"!==s.direction&&"sendonly"!==s.direction)continue;const t={uri:s.uri,id:s.sendId,encrypt:s.encrypt,parameters:{}};r.headerExtensions.push(t)}return r},t.getSendingRemoteRtpParameters=function(e,t){const r={mid:void 0,codecs:[],headerExtensions:[],encodings:[],rtcp:{}};for(const s of t.codecs){if(s.kind!==e)continue;const t={mimeType:s.mimeType,payloadType:s.localPayloadType,clockRate:s.clockRate,channels:s.channels,parameters:s.remoteParameters,rtcpFeedback:s.rtcpFeedback};if(r.codecs.push(t),s.localRtxPayloadType){const e={mimeType:`${s.kind}/rtx`,payloadType:s.localRtxPayloadType,clockRate:s.clockRate,parameters:{apt:s.localPayloadType},rtcpFeedback:[]};r.codecs.push(e)}}for(const s of t.headerExtensions){if(s.kind&&s.kind!==e||"sendrecv"!==s.direction&&"sendonly"!==s.direction)continue;const t={uri:s.uri,id:s.sendId,encrypt:s.encrypt,parameters:{}};r.headerExtensions.push(t)}if(r.headerExtensions.some(e=>"http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01"===e.uri))for(const e of r.codecs)e.rtcpFeedback=(e.rtcpFeedback??[]).filter(e=>"goog-remb"!==e.type);else if(r.headerExtensions.some(e=>"http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time"===e.uri))for(const e of r.codecs)e.rtcpFeedback=(e.rtcpFeedback??[]).filter(e=>"transport-cc"!==e.type);else for(const e of r.codecs)e.rtcpFeedback=(e.rtcpFeedback??[]).filter(e=>"transport-cc"!==e.type&&"goog-remb"!==e.type);return r},t.reduceCodecs=function(e,t){const r=[];if(t){for(let s=0;s<e.length;++s)if(g(e[s],t,{strict:!0})){r.push(e[s]),f(e[s+1])&&r.push(e[s+1]);break}if(0===r.length)throw new TypeError("no matching codec found")}else r.push(e[0]),f(e[1])&&r.push(e[1]);return r},t.generateProbatorRtpParameters=function(e){c(e=i.clone(e));const t={mid:a,codecs:[],headerExtensions:[],encodings:[{ssrc:n}],rtcp:{cname:"probator"}};return t.codecs.push(e.codecs[0]),t.codecs[0].payloadType=o,t.headerExtensions=e.headerExtensions,t},t.canSend=function(e,t){return(t.codecs??[]).some(t=>t.kind===e)},t.canReceive=function(e,t){if(c(e),0===e.codecs.length)return!1;const r=e.codecs[0];return(t.codecs??[]).some(e=>e.preferredPayloadType===r.payloadType)};const s=r(3200),i=r(1765),a="probator",n=1234,o=127;function c(e){if("object"!=typeof e)throw new TypeError("params is not an object");if(e.mid&&"string"!=typeof e.mid)throw new TypeError("params.mid is not a string");if(!Array.isArray(e.codecs))throw new TypeError("missing params.codecs");for(const t of e.codecs)h(t);if(e.headerExtensions&&!Array.isArray(e.headerExtensions))throw new TypeError("params.headerExtensions is not an array");e.headerExtensions||(e.headerExtensions=[]);for(const t of e.headerExtensions)u(t);if(e.encodings&&!Array.isArray(e.encodings))throw new TypeError("params.encodings is not an array");e.encodings||(e.encodings=[]);for(const t of e.encodings)m(t);if(e.rtcp&&"object"!=typeof e.rtcp)throw new TypeError("params.rtcp is not an object");e.rtcp||(e.rtcp={}),function(e){if("object"!=typeof e)throw new TypeError("rtcp is not an object");if(e.cname&&"string"!=typeof e.cname)throw new TypeError("invalid rtcp.cname");e.reducedSize&&"boolean"==typeof e.reducedSize||(e.reducedSize=!0)}(e.rtcp)}function d(e){const t=new RegExp("^(audio|video)/(.+)","i");if("object"!=typeof e)throw new TypeError("codec is not an object");if(!e.mimeType||"string"!=typeof e.mimeType)throw new TypeError("missing codec.mimeType");const r=t.exec(e.mimeType);if(!r)throw new TypeError("invalid codec.mimeType");if(e.kind=r[1].toLowerCase(),"number"!=typeof e.preferredPayloadType)throw new TypeError("missing codec.preferredPayloadType");if("number"!=typeof e.clockRate)throw new TypeError("missing codec.clockRate");"audio"===e.kind?"number"!=typeof e.channels&&(e.channels=1):delete e.channels,e.parameters&&"object"==typeof e.parameters||(e.parameters={});for(const t of Object.keys(e.parameters)){let r=e.parameters[t];if(void 0===r&&(e.parameters[t]="",r=""),"string"!=typeof r&&"number"!=typeof r)throw new TypeError(`invalid codec parameter [key:${t}s, value:${r}]`);if("apt"===t&&"number"!=typeof r)throw new TypeError("invalid codec apt parameter")}e.rtcpFeedback&&Array.isArray(e.rtcpFeedback)||(e.rtcpFeedback=[]);for(const t of e.rtcpFeedback)p(t)}function p(e){if("object"!=typeof e)throw new TypeError("fb is not an object");if(!e.type||"string"!=typeof e.type)throw new TypeError("missing fb.type");e.parameter&&"string"==typeof e.parameter||(e.parameter="")}function l(e){if("object"!=typeof e)throw new TypeError("ext is not an object");if("audio"!==e.kind&&"video"!==e.kind)throw new TypeError("invalid ext.kind");if(!e.uri||"string"!=typeof e.uri)throw new TypeError("missing ext.uri");if("number"!=typeof e.preferredId)throw new TypeError("missing ext.preferredId");if(e.preferredEncrypt&&"boolean"!=typeof e.preferredEncrypt)throw new TypeError("invalid ext.preferredEncrypt");if(e.preferredEncrypt||(e.preferredEncrypt=!1),e.direction&&"string"!=typeof e.direction)throw new TypeError("invalid ext.direction");e.direction||(e.direction="sendrecv")}function h(e){const t=new RegExp("^(audio|video)/(.+)","i");if("object"!=typeof e)throw new TypeError("codec is not an object");if(!e.mimeType||"string"!=typeof e.mimeType)throw new TypeError("missing codec.mimeType");const r=t.exec(e.mimeType);if(!r)throw new TypeError("invalid codec.mimeType");if("number"!=typeof e.payloadType)throw new TypeError("missing codec.payloadType");if("number"!=typeof e.clockRate)throw new TypeError("missing codec.clockRate");"audio"===r[1].toLowerCase()?"number"!=typeof e.channels&&(e.channels=1):delete e.channels,e.parameters&&"object"==typeof e.parameters||(e.parameters={});for(const t of Object.keys(e.parameters)){let r=e.parameters[t];if(void 0===r&&(e.parameters[t]="",r=""),"string"!=typeof r&&"number"!=typeof r)throw new TypeError(`invalid codec parameter [key:${t}s, value:${r}]`);if("apt"===t&&"number"!=typeof r)throw new TypeError("invalid codec apt parameter")}e.rtcpFeedback&&Array.isArray(e.rtcpFeedback)||(e.rtcpFeedback=[]);for(const t of e.rtcpFeedback)p(t)}function u(e){if("object"!=typeof e)throw new TypeError("ext is not an object");if(!e.uri||"string"!=typeof e.uri)throw new TypeError("missing ext.uri");if("number"!=typeof e.id)throw new TypeError("missing ext.id");if(e.encrypt&&"boolean"!=typeof e.encrypt)throw new TypeError("invalid ext.encrypt");e.encrypt||(e.encrypt=!1),e.parameters&&"object"==typeof e.parameters||(e.parameters={});for(const t of Object.keys(e.parameters)){let r=e.parameters[t];if(void 0===r&&(e.parameters[t]="",r=""),"string"!=typeof r&&"number"!=typeof r)throw new TypeError("invalid header extension parameter")}}function m(e){if("object"!=typeof e)throw new TypeError("encoding is not an object");if(e.ssrc&&"number"!=typeof e.ssrc)throw new TypeError("invalid encoding.ssrc");if(e.rid&&"string"!=typeof e.rid)throw new TypeError("invalid encoding.rid");if(e.rtx&&"object"!=typeof e.rtx)throw new TypeError("invalid encoding.rtx");if(e.rtx&&"number"!=typeof e.rtx.ssrc)throw new TypeError("missing encoding.rtx.ssrc");if(e.dtx&&"boolean"==typeof e.dtx||(e.dtx=!1),e.scalabilityMode&&"string"!=typeof e.scalabilityMode)throw new TypeError("invalid encoding.scalabilityMode")}function f(e){return!!e&&/.+\/rtx$/i.test(e.mimeType)}function g(e,t,{strict:r=!1,modify:i=!1}={}){const a=e.mimeType.toLowerCase();if(a!==t.mimeType.toLowerCase())return!1;if(e.clockRate!==t.clockRate)return!1;if(e.channels!==t.channels)return!1;switch(a){case"video/h264":if(r){if((e.parameters["packetization-mode"]??0)!==(t.parameters["packetization-mode"]??0))return!1;if(!s.isSameProfile(e.parameters,t.parameters))return!1;let r;try{r=s.generateProfileLevelIdStringForAnswer(e.parameters,t.parameters)}catch(e){return!1}i&&(r?(e.parameters["profile-level-id"]=r,t.parameters["profile-level-id"]=r):(delete e.parameters["profile-level-id"],delete t.parameters["profile-level-id"]))}break;case"video/vp9":if(r&&(e.parameters["profile-id"]??0)!==(t.parameters["profile-id"]??0))return!1}return!0}function _(e,t){const r=[];for(const s of e.rtcpFeedback??[]){const e=(t.rtcpFeedback??[]).find(e=>e.type===s.type&&(e.parameter===s.parameter||!e.parameter&&!s.parameter));e&&r.push(e)}return r}},8057:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0})},8274:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Logger=void 0;const s=r(7833),i="awaitqueue";t.Logger=class{_debug;_warn;_error;constructor(e){e?(this._debug=s(`${i}:${e}`),this._warn=s(`${i}:WARN:${e}`),this._error=s(`${i}:ERROR:${e}`)):(this._debug=s(i),this._warn=s(`${i}:WARN`),this._error=s(`${i}:ERROR`)),this._debug.log=console.info.bind(console),this._warn.log=console.warn.bind(console),this._error.log=console.error.bind(console)}get debug(){return this._debug}get warn(){return this._warn}get error(){return this._error}}},8286:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const s=r(3779),i=r(2291),a=r(6011);t.default=function(e,t,r){if(s.default.randomUUID&&!t&&!e)return s.default.randomUUID();const n=(e=e||{}).random??e.rng?.()??(0,i.default)();if(n.length<16)throw new Error("Random bytes length must be >= 16");if(n[6]=15&n[6]|64,n[8]=63&n[8]|128,t){if((r=r||0)<0||r+16>t.length)throw new RangeError(`UUID byte range ${r}:${r+15} is out of buffer bounds`);for(let e=0;e<16;++e)t[r+e]=n[e];return t}return(0,a.unsafeStringify)(n)}},8876:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.AwaitQueueRemovedTaskError=t.AwaitQueueStoppedError=t.AwaitQueue=void 0;var s=r(9275);Object.defineProperty(t,"AwaitQueue",{enumerable:!0,get:function(){return s.AwaitQueue}});var i=r(4253);Object.defineProperty(t,"AwaitQueueStoppedError",{enumerable:!0,get:function(){return i.AwaitQueueStoppedError}}),Object.defineProperty(t,"AwaitQueueRemovedTaskError",{enumerable:!0,get:function(){return i.AwaitQueueRemovedTaskError}})},9166:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DataConsumer=void 0;const s=r(2994),i=r(3953),a=new s.Logger("DataConsumer");class n extends i.EnhancedEventEmitter{_id;_dataProducerId;_dataChannel;_closed=!1;_sctpStreamParameters;_appData;_observer=new i.EnhancedEventEmitter;constructor({id:e,dataProducerId:t,dataChannel:r,sctpStreamParameters:s,appData:i}){super(),a.debug("constructor()"),this._id=e,this._dataProducerId=t,this._dataChannel=r,this._sctpStreamParameters=s,this._appData=i??{},this.handleDataChannel()}get id(){return this._id}get dataProducerId(){return this._dataProducerId}get closed(){return this._closed}get sctpStreamParameters(){return this._sctpStreamParameters}get readyState(){return this._dataChannel.readyState}get label(){return this._dataChannel.label}get protocol(){return this._dataChannel.protocol}get binaryType(){return this._dataChannel.binaryType}set binaryType(e){this._dataChannel.binaryType=e}get appData(){return this._appData}set appData(e){this._appData=e}get observer(){return this._observer}close(){this._closed||(a.debug("close()"),this._closed=!0,this._dataChannel.close(),this.emit("@close"),this._observer.safeEmit("close"),super.close(),this._observer.close())}transportClosed(){this._closed||(a.debug("transportClosed()"),this._closed=!0,this._dataChannel.close(),this.safeEmit("transportclose"),this._observer.safeEmit("close"))}handleDataChannel(){this._dataChannel.addEventListener("open",()=>{this._closed||(a.debug('DataChannel "open" event'),this.safeEmit("open"))}),this._dataChannel.addEventListener("error",e=>{if(this._closed)return;const t=e.error??new Error("unknown DataChannel error");"sctp-failure"===e.error?.errorDetail?a.error("DataChannel SCTP error [sctpCauseCode:%s]: %s",e.error?.sctpCauseCode,e.error.message):a.error('DataChannel "error" event: %o',t),this.safeEmit("error",t)}),this._dataChannel.addEventListener("close",()=>{this._closed||(a.warn('DataChannel "close" event'),this._closed=!0,this.emit("@close"),this.safeEmit("close"),this._observer.safeEmit("close"))}),this._dataChannel.addEventListener("message",e=>{this._closed||this.safeEmit("message",e.data)})}}t.DataConsumer=n},9275:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.AwaitQueue=void 0;const s=r(8274),i=r(4253),a=new s.Logger("AwaitQueue");t.AwaitQueue=class{pendingTasks=new Map;nextTaskId=0;stopping=!1;constructor(){a.debug("constructor()")}get size(){return this.pendingTasks.size}async push(e,t){if(t=t??e.name,a.debug(`push() [name:${t}]`),"function"!=typeof e)throw new TypeError("given task is not a function");if(t)try{Object.defineProperty(e,"name",{value:t})}catch(e){}return new Promise((r,s)=>{const i={id:this.nextTaskId++,task:e,name:t,enqueuedAt:Date.now(),executedAt:void 0,completed:!1,resolve:e=>{if(i.completed)return;i.completed=!0,this.pendingTasks.delete(i.id),a.debug(`resolving task [name:${i.name}]`),r(e);const[t]=this.pendingTasks.values();t&&!t.executedAt&&this.execute(t)},reject:e=>{if(!i.completed&&(i.completed=!0,this.pendingTasks.delete(i.id),a.debug(`rejecting task [name:${i.name}]: %s`,String(e)),s(e),!this.stopping)){const[e]=this.pendingTasks.values();e&&!e.executedAt&&this.execute(e)}}};this.pendingTasks.set(i.id,i),1===this.pendingTasks.size&&this.execute(i)})}stop(){a.debug("stop()"),this.stopping=!0;for(const e of this.pendingTasks.values())a.debug(`stop() | stopping task [name:${e.name}]`),e.reject(new i.AwaitQueueStoppedError);this.stopping=!1}remove(e){a.debug(`remove() [taskIdx:${e}]`);const t=Array.from(this.pendingTasks.values())[e];t?t.reject(new i.AwaitQueueRemovedTaskError):a.debug(`stop() | no task with given idx [taskIdx:${e}]`)}dump(){const e=Date.now();let t=0;return Array.from(this.pendingTasks.values()).map(r=>({idx:t++,task:r.task,name:r.name,enqueuedTime:r.executedAt?r.executedAt-r.enqueuedAt:e-r.enqueuedAt,executionTime:r.executedAt?e-r.executedAt:0}))}async execute(e){if(a.debug(`execute() [name:${e.name}]`),e.executedAt)throw new Error("task already being executed");e.executedAt=Date.now();try{const t=await e.task();e.resolve(t)}catch(t){e.reject(t)}}}},9746:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const s=r(6697);t.default=function(e){return"string"==typeof e&&s.default.test(e)}},9792:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Producer=void 0;const s=r(2994),i=r(3953),a=r(4893),n=new s.Logger("Producer");class o extends i.EnhancedEventEmitter{_id;_localId;_closed=!1;_rtpSender;_track;_kind;_rtpParameters;_paused;_maxSpatialLayer;_stopTracks;_disableTrackOnPause;_zeroRtpOnPause;_appData;_observer=new i.EnhancedEventEmitter;constructor({id:e,localId:t,rtpSender:r,track:s,rtpParameters:i,stopTracks:a,disableTrackOnPause:o,zeroRtpOnPause:c,appData:d}){super(),n.debug("constructor()"),this._id=e,this._localId=t,this._rtpSender=r,this._track=s,this._kind=s.kind,this._rtpParameters=i,this._paused=!!o&&!s.enabled,this._maxSpatialLayer=void 0,this._stopTracks=a,this._disableTrackOnPause=o,this._zeroRtpOnPause=c,this._appData=d??{},this.onTrackEnded=this.onTrackEnded.bind(this),this.handleTrack()}get id(){return this._id}get localId(){return this._localId}get closed(){return this._closed}get kind(){return this._kind}get rtpSender(){return this._rtpSender}get track(){return this._track}get rtpParameters(){return this._rtpParameters}get paused(){return this._paused}get maxSpatialLayer(){return this._maxSpatialLayer}get appData(){return this._appData}set appData(e){this._appData=e}get observer(){return this._observer}close(){this._closed||(n.debug("close()"),this._closed=!0,this.destroyTrack(),this.emit("@close"),this._observer.safeEmit("close"),super.close(),this._observer.close())}transportClosed(){this._closed||(n.debug("transportClosed()"),this._closed=!0,this.destroyTrack(),this.safeEmit("transportclose"),this._observer.safeEmit("close"))}async getStats(){if(this._closed)throw new a.InvalidStateError("closed");return new Promise((e,t)=>{this.safeEmit("@getstats",e,t)})}pause(){n.debug("pause()"),this._closed?n.error("pause() | Producer closed"):(this._paused=!0,this._track&&this._disableTrackOnPause&&(this._track.enabled=!1),this._zeroRtpOnPause&&new Promise((e,t)=>{this.safeEmit("@pause",e,t)}).catch(()=>{}),this._observer.safeEmit("pause"))}resume(){n.debug("resume()"),this._closed?n.error("resume() | Producer closed"):(this._paused=!1,this._track&&this._disableTrackOnPause&&(this._track.enabled=!0),this._zeroRtpOnPause&&new Promise((e,t)=>{this.safeEmit("@resume",e,t)}).catch(()=>{}),this._observer.safeEmit("resume"))}async replaceTrack({track:e}){if(n.debug("replaceTrack() [track:%o]",e),this._closed){if(e&&this._stopTracks)try{e.stop()}catch(e){}throw new a.InvalidStateError("closed")}if(e&&"ended"===e.readyState)throw new a.InvalidStateError("track ended");e!==this._track?(await new Promise((t,r)=>{this.safeEmit("@replacetrack",e,t,r)}),this.destroyTrack(),this._track=e,this._track&&this._disableTrackOnPause&&(this._paused?this._paused&&(this._track.enabled=!1):this._track.enabled=!0),this.handleTrack()):n.debug("replaceTrack() | same track, ignored")}async setMaxSpatialLayer(e){if(this._closed)throw new a.InvalidStateError("closed");if("video"!==this._kind)throw new a.UnsupportedError("not a video Producer");if("number"!=typeof e)throw new TypeError("invalid spatialLayer");e!==this._maxSpatialLayer&&(await new Promise((t,r)=>{this.safeEmit("@setmaxspatiallayer",e,t,r)}).catch(()=>{}),this._maxSpatialLayer=e)}async setRtpEncodingParameters(e){if(this._closed)throw new a.InvalidStateError("closed");if("object"!=typeof e)throw new TypeError("invalid params");await new Promise((t,r)=>{this.safeEmit("@setrtpencodingparameters",e,t,r)})}onTrackEnded(){n.debug('track "ended" event'),this.safeEmit("trackended"),this._observer.safeEmit("trackended")}handleTrack(){this._track&&this._track.addEventListener("ended",this.onTrackEnded)}destroyTrack(){if(this._track)try{this._track.removeEventListener("ended",this.onTrackEnded),this._stopTracks&&this._track.stop()}catch(e){}}}t.Producer=o}},t={};function r(s){var i=t[s];if(void 0!==i)return i.exports;var a=t[s]={exports:{}};return e[s].call(a.exports,a,a.exports,r),a.exports}r.amdO={};var s={};window.mediasoup=r(76);var i=window;for(var a in s)i[a]=s[a];s.__esModule&&Object.defineProperty(i,"__esModule",{value:!0})})();