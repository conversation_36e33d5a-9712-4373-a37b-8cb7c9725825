http:
  middlewares:
    jwt-auth-authenticated:
      plugin:
        jwt:
          secret: super-secret-jwt-token-with-at-least-32-characters-long
          issuers:
            - http://host.docker.internal:54321/auth/v1/
          parameterName: token
          require:
            aud: authenticated

    jwt-auth-role-administrator:
      plugin:
        jwt:
          secret: super-secret-jwt-token-with-at-least-32-characters-long
          issuers:
            - http://host.docker.internal:54321/auth/v1/
          require:
            aud: authenticated
            app_role: administrator

    jwt-auth-role-manager:
      plugin:
        jwt:
          secret: super-secret-jwt-token-with-at-least-32-characters-long
          parameterName: token
          issuers:
            - http://host.docker.internal:54321/auth/v1/
          require:
            aud: authenticated
            app_role: manager