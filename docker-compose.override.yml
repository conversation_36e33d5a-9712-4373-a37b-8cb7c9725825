services:
  user-management:
    build:
      context: ./services.user-management
      dockerfile: Dockerfile
    env_file:
      - .env.supabase.docker

  call-orchestrator:
    build:
      context: ./services.call-orchestrator
      dockerfile: Dockerfile
    env_file:
      - .env.supabase.docker
    depends_on:
      kafka:
        condition: service_started
      call-orchestrator-init:
        condition: service_completed_successfully

  voice-router:
    build:
      context: ./services.voice-router
      dockerfile: Dockerfile

  queuing-service:
    build:
      context: ./services.queuing-service
      dockerfile: Dockerfile
    environment:
      - PARTICIPANT_PRESENCE_URL=http://participant-presence:8001

  participant-presence:
    build:
      context: ./services.participant-presence
      dockerfile: Dockerfile
    env_file:
      - .env.supabase.docker

  mock-caller:
    build:
      context: ./caller
      dockerfile: Dockerfile
    environment:
      - KAFKA_BOOTSTRAP_SERVERS=kafka:29092
      - SERVICE_NAME=mock-caller
      - VOICE_ROUTER_WS=ws://voice-router:3001
    networks:
      - internal
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.mock-caller.rule=Host(`localhost`) && PathPrefix(`/caller`)"
      - "traefik.http.routers.mock-caller.entrypoints=web"
      - "traefik.http.services.mock-caller.loadbalancer.server.port=8010"
    ports:
      - "8010:8010"


  postgres:
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U user -d cortexa"]
      interval: 5s
      timeout: 5s
      retries: 20
    environment:
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
      - POSTGRES_DB=cortexa
    env_file:
      - .env.supabase.docker

  call-orchestrator-init:
    build:
      context: ./services.call-orchestrator
      dockerfile: Dockerfile
      target: builder
    environment:
      - NODE_ENV=development
      - DATABASE_URL=****************************************/cortexa
    depends_on:
      postgres:
        condition: service_healthy
    command: >
      sh -lc "set -e; npx prisma migrate deploy; (npx prisma db seed || echo 'No Prisma seed configured, skipping seeding.');"
    restart: "no"
    networks:
      - internal